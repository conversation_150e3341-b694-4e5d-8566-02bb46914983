# 🔐 Dazzled Platform - Complete Environment Variables Guide

This document contains all environment variables used across the Dazzled e-commerce platform, including frontend, backend, and optional services.

## 📁 File Structure

```
dazzled/
├── .env.local                 # Frontend environment variables
└── backend/
    └── .env                   # Backend environment variables
```

## 🎯 Frontend Environment Variables (`.env.local`)

### Required Variables

```bash
# =============================================================================
# API CONFIGURATION
# =============================================================================
NEXT_PUBLIC_API_URL=http://localhost:3002/api
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3001

# =============================================================================
# GOOGLE OAUTH (Required for authentication)
# =============================================================================
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
```

### Optional Variables

```bash
# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV=development
NEXT_PUBLIC_APP_NAME=Dazzled
NEXT_PUBLIC_APP_VERSION=1.0.0

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# =============================================================================
# FEATURE FLAGS
# =============================================================================
NEXT_PUBLIC_ENABLE_CUSTOM_ORDERS=true
NEXT_PUBLIC_ENABLE_WISHLIST=true
NEXT_PUBLIC_ENABLE_REVIEWS=true
NEXT_PUBLIC_ENABLE_ADMIN_DASHBOARD=true

# =============================================================================
# SEO AND SOCIAL MEDIA
# =============================================================================
NEXT_PUBLIC_SITE_NAME=Dazzled - Rhinestone Bedazzled Portraits
NEXT_PUBLIC_SITE_DESCRIPTION=Transform your memories into stunning rhinestone bedazzled portraits
NEXT_PUBLIC_SITE_URL=https://dazzled.com

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_SHOW_DEV_TOOLS=true

# =============================================================================
# ANALYTICS (Optional)
# =============================================================================
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
NEXT_PUBLIC_HOTJAR_ID=your-hotjar-id
```

## 🚀 Backend Environment Variables (`backend/.env`)

### Required Variables

```bash
# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
MONGODB_URI=mongodb+srv://username:<EMAIL>/dazzled?retryWrites=true&w=majority
# Local alternative: mongodb://localhost:27017/dazzled

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
JWT_SECRET=your-super-secure-jwt-secret-64-characters-minimum-recommended
JWT_EXPIRES_IN=7d

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
PORT=3002
NODE_ENV=development
FRONTEND_URL=http://localhost:3001

# =============================================================================
# GOOGLE OAUTH CONFIGURATION
# =============================================================================
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-your-google-client-secret
```

### Optional Variables

```bash
# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_FILE_SIZE=10485760
UPLOAD_DEST=./uploads

# =============================================================================
# EMAIL CONFIGURATION (Choose one option)
# =============================================================================

# Option A: Gmail SMTP (Development)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-16-character-app-password
SMTP_FROM=<EMAIL>
SMTP_FROM_NAME=Dazzled

# Option B: SendGrid (Production)
SENDGRID_API_KEY=SG.your-sendgrid-api-key

# =============================================================================
# PAYMENT PROCESSING (Future Implementation)
# =============================================================================

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# M-Pesa Configuration (Kenya)
MPESA_CONSUMER_KEY=your-mpesa-consumer-key
MPESA_CONSUMER_SECRET=your-mpesa-consumer-secret
MPESA_SHORTCODE=your-business-shortcode
MPESA_PASSKEY=your-mpesa-passkey
MPESA_CALLBACK_URL=https://yourdomain.com/api/payments/mpesa/callback

# =============================================================================
# CLOUD STORAGE (Production - Choose one)
# =============================================================================

# Option A: AWS S3
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=dazzled-uploads
AWS_REGION=us-east-1

# Option B: Cloudinary
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# =============================================================================
# MONITORING AND ANALYTICS
# =============================================================================

# Error Tracking
SENTRY_DSN=https://<EMAIL>/project-id

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# =============================================================================
# RATE LIMITING AND SECURITY
# =============================================================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGINS=http://localhost:3001,https://yourdomain.com

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600
```

## 🔧 Service Setup Instructions

### 1. MongoDB Setup
- **Local**: Install MongoDB locally or use MongoDB Compass
- **Cloud**: Use MongoDB Atlas (recommended)
  - Create cluster at https://cloud.mongodb.com/
  - Get connection string and replace username/password

### 2. Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3001/auth/callback` (development)
   - `https://yourdomain.com/auth/callback` (production)

### 3. Email Service Setup

#### Gmail SMTP (Development)
1. Enable 2-Factor Authentication
2. Generate App Password:
   - Google Account → Security → 2-Step Verification → App passwords
   - Generate password for "Mail"

#### SendGrid (Production)
1. Sign up at https://sendgrid.com/
2. Create API key with full access
3. Verify sender identity

### 4. Payment Setup (Future)

#### Stripe
1. Sign up at https://stripe.com/
2. Get test API keys from Dashboard → Developers → API keys
3. Set up webhooks for payment events

#### M-Pesa (Kenya)
1. Register with Safaricom for M-Pesa API
2. Get sandbox credentials for testing
3. Configure callback URLs

### 5. Cloud Storage (Production)

#### AWS S3
1. Create AWS account and S3 bucket
2. Create IAM user with S3 permissions
3. Generate access keys

#### Cloudinary
1. Sign up at https://cloudinary.com/
2. Get credentials from dashboard

## 🔒 Security Best Practices

### JWT Secret Generation
```bash
# Generate secure JWT secret (64+ characters)
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

### Environment Security
- ✅ Never commit `.env` files to version control
- ✅ Use different secrets for development and production
- ✅ Rotate secrets regularly in production
- ✅ Use environment variable management tools (AWS Secrets Manager, etc.)
- ✅ Enable HTTPS for all production endpoints
- ✅ Set up proper database access controls

## 🚀 Quick Setup Commands

```bash
# 1. Copy environment files
cp .env.local.example .env.local
cp backend/.env.example backend/.env

# 2. Install dependencies concurrently
pnpm install:concurrent

# 3. Validate environment setup
pnpm validate-env

# 4. Start development servers
pnpm dev
```

## 🆘 Troubleshooting

### Common Issues
- **MongoDB connection failed**: Check connection string and network access
- **Google OAuth not working**: Verify redirect URIs match exactly
- **CORS errors**: Ensure frontend URL is in backend CORS config
- **File upload fails**: Check upload directory permissions
- **Email not sending**: Verify SMTP credentials and 2FA setup

### Validation
Run the environment validator to check your setup:
```bash
pnpm validate-env
```

---

**📝 Note**: This file contains all possible environment variables. Only the "Required Variables" sections are needed for basic functionality. Optional variables can be added as needed for additional features.
