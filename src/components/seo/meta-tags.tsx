import { Metadata } from 'next';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product';
  price?: number;
  currency?: string;
  availability?: 'in_stock' | 'out_of_stock' | 'preorder';
}

const defaultSEO = {
  title: 'Bedazzled - Premium Rhinestone Bedazzled Portraits',
  description: 'Transform your precious memories into stunning, one-of-a-kind rhinestone artwork. Each piece is meticulously handcrafted by our Kenyan artisans with premium materials.',
  keywords: ['bedazzled', 'rhinestone', 'portraits', 'custom art', 'personalized gifts', 'kenya', 'handcrafted', 'premium'],
  image: '/favicon.ico',
  url: 'https://bedazzled.co.ke',
  type: 'website' as const,
  currency: 'KES'
};

export function generateMetadata({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  price,
  currency = 'KES',
  availability = 'in_stock'
}: SEOProps = {}): Metadata {
  const seo = {
    title: title ? `${title} | ${defaultSEO.title}` : defaultSEO.title,
    description: description || defaultSEO.description,
    keywords: [...defaultSEO.keywords, ...keywords],
    image: image || defaultSEO.image,
    url: url || defaultSEO.url,
    type,
    price,
    currency,
    availability
  };

  const metadata: Metadata = {
    title: seo.title,
    description: seo.description,
    keywords: seo.keywords,
    authors: [{ name: 'Bedazzled Team' }],
    creator: 'Bedazzled',
    publisher: 'Bedazzled',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(defaultSEO.url),
    alternates: {
      canonical: url || '/',
    },
    icons: {
      icon: '/favicon.ico',
      shortcut: '/favicon.ico',
      apple: '/favicon.ico',
    },
    manifest: '/site.webmanifest',
    openGraph: {
      type: seo.type,
      locale: 'en_KE',
      url: seo.url,
      title: seo.title,
      description: seo.description,
      siteName: 'Bedazzled',
      images: [
        {
          url: seo.image,
          width: 1200,
          height: 630,
          alt: seo.title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: seo.title,
      description: seo.description,
      images: [seo.image],
      creator: '@bedazzled_ke',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };

  // Add product-specific metadata
  if (type === 'product' && price) {
    metadata.other = {
      'product:price:amount': price.toString(),
      'product:price:currency': currency,
      'product:availability': availability,
      'product:condition': 'new',
      'product:brand': 'Bedazzled',
      'product:category': 'Art & Collectibles',
    };
  }

  return metadata;
}

// Structured data generators
export function generateProductStructuredData(product: {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  image: string;
  availability: 'in_stock' | 'out_of_stock';
  rating?: number;
  reviewCount?: number;
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    '@id': `https://bedazzled.co.ke/products/${product.id}`,
    name: product.name,
    description: product.description,
    image: product.image,
    brand: {
      '@type': 'Brand',
      name: 'Bedazzled'
    },
    offers: {
      '@type': 'Offer',
      price: product.price,
      priceCurrency: product.currency,
      availability: product.availability === 'in_stock' 
        ? 'https://schema.org/InStock' 
        : 'https://schema.org/OutOfStock',
      seller: {
        '@type': 'Organization',
        name: 'Bedazzled'
      }
    },
    ...(product.rating && {
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingValue: product.rating,
        reviewCount: product.reviewCount || 1,
        bestRating: 5,
        worstRating: 1
      }
    })
  };
}

export function generateOrganizationStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Bedazzled',
    url: 'https://bedazzled.co.ke',
    logo: 'https://bedazzled.co.ke/favicon.ico',
    description: 'Premium rhinestone bedazzled portraits handcrafted by Kenyan artisans',
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'KE',
      addressLocality: 'Nairobi'
    },
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+254-700-000-000',
      contactType: 'customer service',
      availableLanguage: ['English', 'Swahili']
    },
    sameAs: [
      'https://www.instagram.com/bedazzled_ke',
      'https://www.facebook.com/bedazzled.ke'
    ]
  };
}

export function generateBreadcrumbStructuredData(items: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url
    }))
  };
}
