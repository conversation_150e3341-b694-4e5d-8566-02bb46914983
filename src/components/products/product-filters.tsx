'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FunnelIcon, 
  XMarkIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ProductFilters as FilterType } from '@/types';

interface ProductFiltersProps {
  filters: FilterType;
  onFiltersChange: (filters: FilterType) => void;
  onClearFilters: () => void;
  isOpen: boolean;
  onToggle: () => void;
}

export function ProductFilters({ 
  filters, 
  onFiltersChange, 
  onClearFilters,
  isOpen,
  onToggle 
}: ProductFiltersProps) {
  const [expandedSections, setExpandedSections] = useState<string[]>([
    'category', 'price', 'size', 'bedazzling', 'rating'
  ]);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => 
      prev.includes(section) 
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };

  const updateFilter = (key: keyof FilterType, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const toggleArrayFilter = (key: keyof FilterType, value: string) => {
    const currentArray = (filters[key] as string[]) || [];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    updateFilter(key, newArray);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.category) count++;
    if (filters.size?.length) count += filters.size.length;
    if (filters.bedazzlingLevel?.length) count += filters.bedazzlingLevel.length;
    if (filters.frameOption?.length) count += filters.frameOption.length;
    if (filters.priceRange) count++;
    if (filters.rating) count++;
    if (filters.inStock) count++;
    if (filters.featured) count++;
    return count;
  };

  const FilterSection = ({ 
    title, 
    sectionKey, 
    children 
  }: { 
    title: string; 
    sectionKey: string; 
    children: React.ReactNode; 
  }) => (
    <div className="border-b border-border last:border-b-0">
      <button
        onClick={() => toggleSection(sectionKey)}
        className="flex items-center justify-between w-full py-4 text-left hover:text-accent transition-colors"
      >
        <span className="font-medium">{title}</span>
        {expandedSections.includes(sectionKey) ? (
          <ChevronUpIcon className="h-4 w-4" />
        ) : (
          <ChevronDownIcon className="h-4 w-4" />
        )}
      </button>
      <AnimatePresence>
        {expandedSections.includes(sectionKey) && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden pb-4"
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );

  const CheckboxOption = ({ 
    label, 
    checked, 
    onChange 
  }: { 
    label: string; 
    checked: boolean; 
    onChange: () => void; 
  }) => (
    <label className="flex items-center space-x-3 cursor-pointer hover:text-accent transition-colors py-1">
      <input
        type="checkbox"
        checked={checked}
        onChange={onChange}
        className="rounded border-border text-accent focus:ring-accent w-4 h-4"
      />
      <span className="text-sm capitalize flex-1">{label}</span>
    </label>
  );

  return (
    <>
      {/* Mobile Filter Toggle */}
      <div className="lg:hidden mb-6">
        <Button
          variant="outline"
          onClick={onToggle}
          className="w-full justify-between"
        >
          <div className="flex items-center">
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filters
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary" className="ml-2">
                {getActiveFiltersCount()}
              </Badge>
            )}
          </div>
          {isOpen ? (
            <ChevronUpIcon className="h-4 w-4" />
          ) : (
            <ChevronDownIcon className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Filter Panel */}
      <AnimatePresence>
        {(isOpen || (typeof window !== 'undefined' && window.innerWidth >= 1024)) && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="lg:block"
          >
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
                <CardTitle className="text-lg">Filters</CardTitle>
                <div className="flex items-center gap-2">
                  {getActiveFiltersCount() > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onClearFilters}
                      className="text-xs"
                    >
                      Clear All
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={onToggle}
                    className="lg:hidden h-6 w-6"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-0">
                {/* Category */}
                <FilterSection title="Category" sectionKey="category">
                  <div className="space-y-2">
                    {['pre-made', 'custom'].map(category => (
                      <CheckboxOption
                        key={category}
                        label={category}
                        checked={filters.category === category}
                        onChange={() => updateFilter('category', 
                          filters.category === category ? undefined : category
                        )}
                      />
                    ))}
                  </div>
                </FilterSection>

                {/* Price Range */}
                <FilterSection title="Price Range (KES)" sectionKey="price">
                  <div className="space-y-3">
                    {/* Quick Price Ranges */}
                    <div className="space-y-2">
                      {[
                        { label: 'Under KES 8,000', min: 0, max: 8000 },
                        { label: 'KES 8,000 - 15,000', min: 8000, max: 15000 },
                        { label: 'KES 15,000 - 25,000', min: 15000, max: 25000 },
                        { label: 'KES 25,000 - 35,000', min: 25000, max: 35000 },
                        { label: 'Over KES 35,000', min: 35000, max: 100000 },
                      ].map(range => (
                        <CheckboxOption
                          key={range.label}
                          label={range.label}
                          checked={
                            filters.priceRange?.[0] === range.min &&
                            filters.priceRange?.[1] === range.max
                          }
                          onChange={() => {
                            if (filters.priceRange?.[0] === range.min &&
                                filters.priceRange?.[1] === range.max) {
                              updateFilter('priceRange', undefined);
                            } else {
                              updateFilter('priceRange', [range.min, range.max]);
                            }
                          }}
                        />
                      ))}
                    </div>

                    {/* Custom Range */}
                    <div className="border-t border-border pt-3">
                      <p className="text-sm text-muted-foreground mb-2">Custom Range</p>
                      <div className="grid grid-cols-2 gap-2">
                        <Input
                          type="number"
                          placeholder="Min KES"
                          value={filters.priceRange?.[0] || ''}
                          onChange={(e) => {
                            const min = parseInt(e.target.value) || 0;
                            const max = filters.priceRange?.[1] || 50000;
                            updateFilter('priceRange', [min, max]);
                          }}
                        />
                        <Input
                          type="number"
                          placeholder="Max KES"
                          value={filters.priceRange?.[1] || ''}
                          onChange={(e) => {
                            const min = filters.priceRange?.[0] || 0;
                            const max = parseInt(e.target.value) || 50000;
                            updateFilter('priceRange', [min, max]);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </FilterSection>

                {/* Size */}
                <FilterSection title="Size" sectionKey="size">
                  <div className="space-y-2">
                    {['small', 'medium', 'large', 'extra-large'].map(size => (
                      <CheckboxOption
                        key={size}
                        label={size}
                        checked={filters.size?.includes(size) || false}
                        onChange={() => toggleArrayFilter('size', size)}
                      />
                    ))}
                  </div>
                </FilterSection>

                {/* Bedazzling Level */}
                <FilterSection title="Bedazzling Level" sectionKey="bedazzling">
                  <div className="space-y-2">
                    {['light', 'medium', 'heavy', 'premium'].map(level => (
                      <CheckboxOption
                        key={level}
                        label={level}
                        checked={filters.bedazzlingLevel?.includes(level) || false}
                        onChange={() => toggleArrayFilter('bedazzlingLevel', level)}
                      />
                    ))}
                  </div>
                </FilterSection>

                {/* Customer Rating */}
                <FilterSection title="Customer Rating" sectionKey="rating">
                  <div className="space-y-2">
                    {[
                      { label: '4.5+ Stars', value: 4.5 },
                      { label: '4.0+ Stars', value: 4.0 },
                      { label: '3.5+ Stars', value: 3.5 },
                      { label: '3.0+ Stars', value: 3.0 }
                    ].map(rating => (
                      <CheckboxOption
                        key={rating.value}
                        label={rating.label}
                        checked={filters.rating === rating.value}
                        onChange={() => {
                          if (filters.rating === rating.value) {
                            updateFilter('rating', undefined);
                          } else {
                            updateFilter('rating', rating.value);
                          }
                        }}
                      />
                    ))}
                  </div>
                </FilterSection>

                {/* Frame Option */}
                <FilterSection title="Frame Option" sectionKey="frame">
                  <div className="space-y-2">
                    {['basic', 'premium', 'luxury'].map(frame => (
                      <CheckboxOption
                        key={frame}
                        label={frame}
                        checked={filters.frameOption?.includes(frame) || false}
                        onChange={() => toggleArrayFilter('frameOption', frame)}
                      />
                    ))}
                  </div>
                </FilterSection>

                {/* Other Options */}
                <FilterSection title="Other Options" sectionKey="other">
                  <div className="space-y-2">
                    <CheckboxOption
                      label="In Stock Only"
                      checked={filters.inStock || false}
                      onChange={() => updateFilter('inStock', !filters.inStock)}
                    />
                    <CheckboxOption
                      label="Featured Only"
                      checked={filters.featured || false}
                      onChange={() => updateFilter('featured', !filters.featured)}
                    />
                  </div>
                </FilterSection>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
