'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ProductFilters as FilterType } from '@/types';

interface ProductFiltersProps {
  filters: FilterType;
  onFiltersChange: (filters: FilterType) => void;
  onClearFilters: () => void;
  isOpen: boolean;
  onToggle: () => void;
}

export function ProductFilters({
  filters,
  onFiltersChange,
  onClearFilters,
  isOpen,
  onToggle
}: ProductFiltersProps) {

  const updateFilter = (key: keyof FilterType, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const hasActiveFilters = () => {
    return filters.category !== undefined;
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Filters</span>
            {hasActiveFilters() && (
              <Button variant="outline" size="sm" onClick={onClearFilters}>
                Clear All
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Category Filter */}
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Category
            </label>
            <select
              value={filters.category || 'all'}
              onChange={(e) => updateFilter('category', e.target.value === 'all' ? undefined : e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground"
            >
              <option value="all">All Categories</option>
              <option value="pre-made">Pre-made</option>
              <option value="custom">Custom</option>
            </select>
          </div>
        </CardContent>
      </Card>
    </div>
  );

