'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import {
  StarIcon,
  ShoppingCartIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { formatPrice } from '@/lib/utils';
import { Product } from '@/types';

interface ProductCardProps {
  product: Product;
  onAddToCart?: (productId: string) => void;
  className?: string;
}

export function ProductCard({
  product,
  onAddToCart,
  className
}: ProductCardProps) {
  const discountPercentage = product.featured ? 15 : 0;
  const discountedPrice = discountPercentage > 0
    ? product.price * (1 - discountPercentage / 100)
    : product.price;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Card className="group overflow-hidden border-0 shadow-md hover:shadow-xl transition-all duration-300">
        <div className="relative aspect-square overflow-hidden bg-muted">
          {/* Product Image */}
          <Link href={`/products/${product.id}`}>
            <div className="relative w-full h-full">
              {product.image ? (
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-accent/20 to-highlight/20 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mb-2">
                      <span className="text-white font-bold text-lg">B</span>
                    </div>
                    <p className="text-sm text-muted-foreground">No Image</p>
                  </div>
                </div>
              )}
            </div>
          </Link>

          {/* Badges */}
          <div className="absolute top-3 left-3 flex flex-col gap-2">
            {product.featured && (
              <Badge variant="destructive" className="text-xs">
                Featured
              </Badge>
            )}
            {discountPercentage > 0 && (
              <Badge variant="success" className="text-xs">
                -{discountPercentage}%
              </Badge>
            )}
            {product.category === 'custom' && (
              <Badge variant="secondary" className="text-xs">
                Custom
              </Badge>
            )}
          </div>

          {/* Action Buttons */}
          <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <Link href={`/products/${product.id}`}>
              <Button
                size="icon"
                variant="outline"
                className="h-8 w-8 bg-white/90 hover:bg-white"
              >
                <EyeIcon className="h-4 w-4" />
              </Button>
            </Link>
          </div>

          {/* Quick Add to Cart */}
          <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <Button
              size="sm"
              className="w-full"
              onClick={() => onAddToCart?.(product.id)}
            >
              <ShoppingCartIcon className="h-4 w-4 mr-2" />
              Quick Add
            </Button>
          </div>
        </div>

        <CardContent className="p-4">
          <div className="space-y-2">
            {/* Product Name */}
            <Link href={`/products/${product.id}`}>
              <h3 className="font-semibold text-foreground hover:text-accent transition-colors line-clamp-2">
                {product.name}
              </h3>
            </Link>

            {/* Rating */}
            <div className="flex items-center gap-1">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <StarIcon
                    key={i}
                    className={`h-4 w-4 ${
                      i < Math.floor(product.rating)
                        ? 'text-warning fill-current'
                        : 'text-muted-foreground'
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-muted-foreground">
                ({product.reviewCount})
              </span>
            </div>

            {/* Price */}
            <div className="flex items-center gap-2">
              <span className="text-lg font-bold text-foreground">
                {formatPrice(discountedPrice)}
              </span>
              {discountPercentage > 0 && (
                <span className="text-sm text-muted-foreground line-through">
                  {formatPrice(defaultVariant.price)}
                </span>
              )}
            </div>

            {/* Variant Info */}
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span className="capitalize">{defaultVariant.size}</span>
              <span>•</span>
              <span className="capitalize">{defaultVariant.bedazzlingLevel}</span>
              <span>•</span>
              <span className="capitalize">{defaultVariant.frameOption}</span>
            </div>

            {/* Stock Status */}
            {defaultVariant.stock > 0 ? (
              <Badge variant="success" className="text-xs w-fit">
                In Stock
              </Badge>
            ) : (
              <Badge variant="destructive" className="text-xs w-fit">
                Out of Stock
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
