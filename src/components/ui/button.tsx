'use client';

import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden',
  {
    variants: {
      variant: {
        default: 'bg-primary text-white hover:bg-primary-light hover:shadow-lg hover:-translate-y-0.5 button-glow',
        destructive: 'bg-error text-white hover:bg-error/90 hover:shadow-lg hover:-translate-y-0.5',
        outline: 'border border-border bg-background hover:bg-muted hover:text-foreground hover:shadow-md hover:-translate-y-0.5',
        secondary: 'bg-accent text-white hover:bg-accent-light hover:shadow-lg hover:-translate-y-0.5 button-glow',
        ghost: 'hover:bg-muted hover:text-foreground hover:shadow-sm',
        link: 'text-primary underline-offset-4 hover:underline',
        accent: 'bg-accent text-white hover:bg-accent-light hover:shadow-lg hover:-translate-y-0.5 button-glow animate-glow',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8 text-base',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
