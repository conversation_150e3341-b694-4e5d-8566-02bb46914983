'use client';

import React from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/auth-context';
import { 
  HomeIcon, 
  ShoppingBagIcon, 
  PaintBrushIcon,
  InformationCircleIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  UserGroupIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  requiresAuth?: boolean;
  adminOnly?: boolean;
  description?: string;
}

export function RoleBasedNavigation() {
  const { user, isAuthenticated } = useAuth();

  // Navigation items for different user types
  const guestNavigation: NavigationItem[] = [
    {
      name: 'Home',
      href: '/',
      icon: HomeIcon,
      description: 'Browse our marketplace'
    },
    // {
    //   name: 'About',
    //   href: '/about',
    //   icon: InformationCircleIcon,
    //   description: 'Learn about Bedazzled'
    // }
  ];

  const authenticatedNavigation: NavigationItem[] = [
    {
      name: 'Home',
      href: '/',
      icon: HomeIcon,
      description: 'Browse our marketplace'
    },
    {
      name: 'Products',
      href: '/products',
      icon: ShoppingBagIcon,
      description: 'Shop pre-made portraits'
    },
    {
      name: 'Custom Orders',
      href: '/custom-orders',
      icon: PaintBrushIcon,
      description: 'Create custom bedazzled portraits'
    },
    // {
    //   name: 'About',
    //   href: '/about',
    //   icon: InformationCircleIcon,
    //   description: 'Learn about Bedazzled'
    // }
  ];

  const adminNavigation: NavigationItem[] = [
    {
      name: 'Dashboard',
      href: '/admin',
      icon: ChartBarIcon,
      adminOnly: true,
      description: 'Admin dashboard overview'
    },
    {
      name: 'Products',
      href: '/admin/products',
      icon: ShoppingBagIcon,
      adminOnly: true,
      description: 'Manage products'
    },
    {
      name: 'Orders',
      href: '/admin/orders',
      icon: ShoppingBagIcon,
      adminOnly: true,
      description: 'Manage orders'
    },
    {
      name: 'Users',
      href: '/admin/users',
      icon: UserGroupIcon,
      adminOnly: true,
      description: 'Manage users'
    }
  ];

  // Determine which navigation to show
  const getNavigation = (): NavigationItem[] => {
    if (!isAuthenticated) {
      return guestNavigation;
    }
    
    if (user?.role === 'admin') {
      return [...authenticatedNavigation, ...adminNavigation];
    }
    
    return authenticatedNavigation;
  };

  const navigation = getNavigation();

  return { navigation, userRole: user?.role || 'guest' };
}

// Hook for getting role-based navigation items
export function useRoleBasedNavigation() {
  return RoleBasedNavigation();
}

// Component for rendering navigation items
interface NavigationListProps {
  items: NavigationItem[];
  className?: string;
  onItemClick?: () => void;
}

export function NavigationList({ items, className = '', onItemClick }: NavigationListProps) {
  const { isAuthenticated } = useAuth();

  const handleNavClick = (item: NavigationItem) => {
    if (item.requiresAuth && !isAuthenticated) {
      window.location.href = '/auth/login';
      return;
    }
    if (onItemClick) {
      onItemClick();
    }
  };

  return (
    <nav className={className}>
      {items.map((item) => {
        const Icon = item.icon;
        return (
          <Link
            key={item.name}
            href={item.href}
            onClick={() => handleNavClick(item)}
            className="flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium text-foreground hover:bg-muted hover:text-accent transition-colors"
          >
            <Icon className="h-5 w-5" />
            <span>{item.name}</span>
            {item.adminOnly && (
              <ShieldCheckIcon className="h-4 w-4 text-accent ml-auto" />
            )}
          </Link>
        );
      })}
    </nav>
  );
}

// Role badge component
export function RoleBadge() {
  const { user, isAuthenticated } = useAuth();

  if (!isAuthenticated || !user) {
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-muted text-muted-foreground">
        Guest
      </span>
    );
  }

  const roleConfig = {
    admin: {
      label: 'Admin',
      className: 'bg-accent text-accent-foreground',
      icon: ShieldCheckIcon
    },
    buyer: {
      label: 'Customer',
      className: 'bg-primary text-primary-foreground',
      icon: UserGroupIcon
    }
  };

  const config = roleConfig[user.role as keyof typeof roleConfig] || roleConfig.buyer;
  const Icon = config.icon;

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.className}`}>
      <Icon className="h-3 w-3 mr-1" />
      {config.label}
    </span>
  );
}
