'use client';

import React from 'react';
import { useAuth } from '@/contexts/auth-context';
import { motion } from 'framer-motion';
import { ShieldCheckIcon, UserIcon, LockClosedIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';

interface RoleBasedLayoutProps {
  children: React.ReactNode;
  requiredRole?: 'admin' | 'buyer' | 'authenticated';
  fallbackComponent?: React.ReactNode;
  showRoleInfo?: boolean;
}

export function RoleBasedLayout({ 
  children, 
  requiredRole, 
  fallbackComponent,
  showRoleInfo = false 
}: RoleBasedLayoutProps) {
  const { user, isAuthenticated, isLoading } = useAuth();

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center animate-pulse">
            <ShieldCheckIcon className="h-8 w-8 text-white" />
          </div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Check authentication requirement
  if (requiredRole === 'authenticated' && !isAuthenticated) {
    return fallbackComponent || <AuthenticationRequired />;
  }

  // Check specific role requirement
  if (requiredRole && requiredRole !== 'authenticated') {
    if (!isAuthenticated) {
      return fallbackComponent || <AuthenticationRequired />;
    }
    
    if (user?.role !== requiredRole) {
      return fallbackComponent || <InsufficientPermissions userRole={user?.role} requiredRole={requiredRole} />;
    }
  }

  // Render children with optional role info
  return (
    <div className="min-h-screen bg-background">
      {showRoleInfo && isAuthenticated && (
        <RoleInfoBanner user={user} />
      )}
      {children}
    </div>
  );
}

// Authentication Required Component
function AuthenticationRequired() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center max-w-md"
      >
        <Card>
          <CardHeader>
            <div className="w-16 h-16 mx-auto bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mb-4">
              <LockClosedIcon className="h-8 w-8 text-white" />
            </div>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              You need to sign in to access this page
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Please sign in to your account to continue. If you don't have an account, 
              you can create one for free.
            </p>
            <div className="flex flex-col gap-3">
              <Link href="/auth/login">
                <Button className="w-full">
                  Sign In
                </Button>
              </Link>
              <Link href="/auth/register">
                <Button variant="outline" className="w-full">
                  Create Account
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}

// Insufficient Permissions Component
interface InsufficientPermissionsProps {
  userRole?: string;
  requiredRole: string;
}

function InsufficientPermissions({ userRole, requiredRole }: InsufficientPermissionsProps) {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center max-w-md"
      >
        <Card>
          <CardHeader>
            <div className="w-16 h-16 mx-auto bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center mb-4">
              <ShieldCheckIcon className="h-8 w-8 text-white" />
            </div>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don't have permission to access this page
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground space-y-2">
              <p>
                This page requires <strong>{requiredRole}</strong> privileges.
              </p>
              {userRole && (
                <p>
                  Your current role: <strong>{userRole}</strong>
                </p>
              )}
            </div>
            <div className="flex flex-col gap-3">
              <Link href="/">
                <Button className="w-full">
                  Go to Homepage
                </Button>
              </Link>
              {userRole === 'buyer' && requiredRole === 'admin' && (
                <p className="text-xs text-muted-foreground">
                  Contact support if you believe you should have admin access.
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}

// Role Info Banner Component
interface RoleInfoBannerProps {
  user: any;
}

function RoleInfoBanner({ user }: RoleInfoBannerProps) {
  const getRoleConfig = (role: string) => {
    switch (role) {
      case 'admin':
        return {
          icon: ShieldCheckIcon,
          label: 'Administrator',
          description: 'Full system access',
          className: 'bg-accent/10 border-accent/20 text-accent-foreground'
        };
      case 'buyer':
        return {
          icon: UserIcon,
          label: 'Customer',
          description: 'Shopping and orders',
          className: 'bg-primary/10 border-primary/20 text-primary-foreground'
        };
      default:
        return {
          icon: UserIcon,
          label: 'User',
          description: 'Basic access',
          className: 'bg-muted border-border text-muted-foreground'
        };
    }
  };

  const config = getRoleConfig(user?.role);
  const Icon = config.icon;

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`border-b px-4 py-2 ${config.className}`}
    >
      <div className="container mx-auto flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Icon className="h-4 w-4" />
          <span className="text-sm font-medium">
            {config.label} Mode
          </span>
          <span className="text-xs opacity-75">
            {config.description}
          </span>
        </div>
        <div className="text-xs opacity-75">
          Signed in as {user?.name}
        </div>
      </div>
    </motion.div>
  );
}

// Role-based conditional rendering hook
export function useRoleAccess() {
  const { user, isAuthenticated } = useAuth();

  const hasRole = (requiredRole: string | string[]) => {
    if (!isAuthenticated || !user) return false;
    
    if (Array.isArray(requiredRole)) {
      return requiredRole.includes(user.role);
    }
    
    return user.role === requiredRole;
  };

  const isAdmin = () => hasRole('admin');
  const isCustomer = () => hasRole('buyer');
  const isAuthenticated_ = () => isAuthenticated;

  return {
    hasRole,
    isAdmin,
    isCustomer,
    isAuthenticated: isAuthenticated_,
    userRole: user?.role,
    user
  };
}

// Role-based component wrapper
interface RoleGateProps {
  children: React.ReactNode;
  allowedRoles: string | string[];
  fallback?: React.ReactNode;
  requireAuth?: boolean;
}

export function RoleGate({ children, allowedRoles, fallback = null, requireAuth = true }: RoleGateProps) {
  const { hasRole, isAuthenticated } = useRoleAccess();

  if (requireAuth && !isAuthenticated) {
    return fallback;
  }

  if (!hasRole(allowedRoles)) {
    return fallback;
  }

  return <>{children}</>;
}
