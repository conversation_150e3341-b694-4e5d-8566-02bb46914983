'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion } from 'framer-motion';
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';

interface BreadcrumbItem {
  label: string;
  href: string;
  icon?: React.ComponentType<any>;
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumb({ items, className = '' }: BreadcrumbProps) {
  const pathname = usePathname();

  // Auto-generate breadcrumbs from pathname if items not provided
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Home', href: '/', icon: HomeIcon }
    ];

    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      
      // Convert segment to readable label
      let label = segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      // Special cases for better labels
      if (segment === 'admin') label = 'Admin Dashboard';
      if (segment === 'custom-orders') label = 'Custom Orders';
      if (segment === 'auth') label = 'Authentication';

      breadcrumbs.push({
        label,
        href: currentPath
      });
    });

    return breadcrumbs;
  };

  const breadcrumbItems = items || generateBreadcrumbs();

  // Don't show breadcrumbs on home page
  if (pathname === '/') {
    return null;
  }

  return (
    <motion.nav
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`flex items-center space-x-1 text-sm text-muted-foreground ${className}`}
      aria-label="Breadcrumb"
    >
      {breadcrumbItems.map((item, index) => {
        const isLast = index === breadcrumbItems.length - 1;
        const Icon = item.icon;

        return (
          <React.Fragment key={item.href}>
            {index > 0 && (
              <ChevronRightIcon className="h-4 w-4 text-muted-foreground/50" />
            )}
            
            {isLast ? (
              <span className="flex items-center space-x-1 text-foreground font-medium">
                {Icon && <Icon className="h-4 w-4" />}
                <span>{item.label}</span>
              </span>
            ) : (
              <Link
                href={item.href}
                className="flex items-center space-x-1 hover:text-foreground transition-colors"
              >
                {Icon && <Icon className="h-4 w-4" />}
                <span>{item.label}</span>
              </Link>
            )}
          </React.Fragment>
        );
      })}
    </motion.nav>
  );
}

// Role-based breadcrumb component
interface RoleBreadcrumbProps {
  className?: string;
}

export function RoleBreadcrumb({ className }: RoleBreadcrumbProps) {
  const pathname = usePathname();

  // Define custom breadcrumbs for specific routes
  const getCustomBreadcrumbs = (): BreadcrumbItem[] | null => {
    if (pathname.startsWith('/admin')) {
      const adminBreadcrumbs: BreadcrumbItem[] = [
        { label: 'Home', href: '/', icon: HomeIcon },
        { label: 'Admin Dashboard', href: '/admin' }
      ];

      if (pathname === '/admin/products') {
        adminBreadcrumbs.push({ label: 'Manage Products', href: '/admin/products' });
      } else if (pathname === '/admin/orders') {
        adminBreadcrumbs.push({ label: 'Manage Orders', href: '/admin/orders' });
      } else if (pathname.startsWith('/admin/products/')) {
        adminBreadcrumbs.push({ label: 'Manage Products', href: '/admin/products' });
        adminBreadcrumbs.push({ label: 'Product Details', href: pathname });
      }

      return adminBreadcrumbs;
    }

    if (pathname.startsWith('/products/')) {
      return [
        { label: 'Home', href: '/', icon: HomeIcon },
        { label: 'Products', href: '/products' },
        { label: 'Product Details', href: pathname }
      ];
    }

    if (pathname === '/custom-orders') {
      return [
        { label: 'Home', href: '/', icon: HomeIcon },
        { label: 'Custom Orders', href: '/custom-orders' }
      ];
    }

    if (pathname === '/cart') {
      return [
        { label: 'Home', href: '/', icon: HomeIcon },
        { label: 'Shopping Cart', href: '/cart' }
      ];
    }

    if (pathname === '/checkout') {
      return [
        { label: 'Home', href: '/', icon: HomeIcon },
        { label: 'Shopping Cart', href: '/cart' },
        { label: 'Checkout', href: '/checkout' }
      ];
    }

    return null;
  };

  const customBreadcrumbs = getCustomBreadcrumbs();

  return (
    <Breadcrumb 
      items={customBreadcrumbs || undefined} 
      className={className} 
    />
  );
}

// Page header with breadcrumbs
interface PageHeaderProps {
  title: string;
  description?: string;
  children?: React.ReactNode;
  showBreadcrumbs?: boolean;
  className?: string;
}

export function PageHeader({ 
  title, 
  description, 
  children, 
  showBreadcrumbs = true,
  className = '' 
}: PageHeaderProps) {
  return (
    <div className={`space-y-4 ${className}`}>
      {showBreadcrumbs && <RoleBreadcrumb />}
      
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <motion.h1 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl lg:text-4xl font-bold text-foreground"
          >
            {title}
          </motion.h1>
          {description && (
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-muted-foreground mt-2"
            >
              {description}
            </motion.p>
          )}
        </div>
        
        {children && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {children}
          </motion.div>
        )}
      </div>
    </div>
  );
}
