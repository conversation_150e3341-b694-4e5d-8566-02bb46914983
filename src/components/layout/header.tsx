'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ShoppingCartIcon,
  UserIcon,
  Bars3Icon,
  XMarkIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  ShoppingBagIcon,
  ClipboardDocumentListIcon,
  SparklesIcon,
  HomeIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/auth-context';
import { useCart } from '@/contexts/cart-context';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  requiresAuth?: boolean;
  roles: string[];
}


export function Header() {
  const { user, isAuthenticated, logout } = useAuth();
  const { itemCount } = useCart();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);

  // Role-based navigation items
  const getNavigationItems = (): NavigationItem[] => {
    const baseNavigation: NavigationItem[] = [
      {
        name: 'Home',
        href: '/',
        icon: HomeIcon,
        requiresAuth: false,
        roles: ['guest', 'user']
      },
      {
        name: 'Products',
        href: '/products',
        icon: ShoppingBagIcon,
        requiresAuth: false,
        roles: ['guest', 'user']
      },
    ];

    const userNavigation: NavigationItem[] = [
      {
        name: 'Custom Orders',
        href: '/custom-orders',
        icon: SparklesIcon,
        requiresAuth: true,
        roles: ['user']
      },
    ];

    const adminNavigation: NavigationItem[] = [
      {
        name: 'Dashboard',
        href: '/admin',
        icon: ChartBarIcon,
        requiresAuth: true,
        roles: ['admin']
      },
      {
        name: 'Manage Products',
        href: '/admin/products',
        icon: Cog6ToothIcon,
        requiresAuth: true,
        roles: ['admin']
      },
      {
        name: 'Orders',
        href: '/admin/orders',
        icon: ClipboardDocumentListIcon,
        requiresAuth: true,
        roles: ['admin']
      },
    ];

    let navigation = [...baseNavigation];

    if (isAuthenticated) {
      navigation = [...navigation, ...userNavigation];

      if (user?.role === 'admin') {
        navigation = [...navigation, ...adminNavigation];
      }
    }

    return navigation.filter(item => {
      if (!item.roles) return true;

      const userRole = user?.role || 'guest';
      return item.roles.includes(userRole);
    });
  };

  const navigation = getNavigationItems();

  const handleNavClick = (href: string, requiresAuth?: boolean) => {
    if (requiresAuth && !isAuthenticated) {
      window.location.href = '/auth/login';
      return;
    }
    window.location.href = href;
  };

  return (
    <div className="sticky top-0 z-50 w-full px-4 pt-4">
      <header className="w-full border border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 rounded-xl shadow-sm">
        <div className="container mx-auto px-6">
          <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <img
              src="/favicon.ico"
              alt="Bedazzled Logo"
              className="h-8 w-8 rounded-full"
            />
            <span className="text-xl font-bold text-primary">Bedazzled</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isAdminRoute = item.href.startsWith('/admin');
              const isActive = pathname === item.href || (item.href !== '/' && pathname.startsWith(item.href));

              return (
                <button
                  key={item.name}
                  onClick={() => handleNavClick(item.href, item.requiresAuth)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    isActive
                      ? isAdminRoute
                        ? 'bg-accent text-accent-foreground shadow-sm'
                        : 'bg-accent/10 text-accent border border-accent/20'
                      : isAdminRoute
                        ? 'text-accent hover:text-accent/80 hover:bg-accent/10 border border-accent/20'
                        : 'text-foreground hover:text-accent hover:bg-accent/5'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.name}</span>
                  {isAdminRoute && (
                    <Badge
                      variant={isActive ? "outline" : "secondary"}
                      className={`ml-1 text-xs ${isActive ? 'border-accent-foreground/20 text-accent-foreground' : ''}`}
                    >
                      Admin
                    </Badge>
                  )}
                </button>
              );
            })}
          </nav>

          {/* Action Buttons */}
          <div className="flex items-center space-x-4">

            {/* Authenticated User Actions */}
            {isAuthenticated && (
              <>
                {/* Cart */}
                <Link href="/cart">
                  <Button variant="ghost" size="icon" className="relative">
                    <ShoppingCartIcon className="h-5 w-5" />
                    {itemCount > 0 && (
                      <Badge
                        variant="destructive"
                        className="absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 text-xs"
                      >
                        {itemCount}
                      </Badge>
                    )}
                  </Button>
                </Link>
              </>
            )}

            {/* User Menu */}
            {isAuthenticated && user ? (
              <div className="relative">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="relative"
                >
                  {user.avatar ? (
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="h-6 w-6 rounded-full"
                    />
                  ) : (
                    <UserIcon className="h-5 w-5" />
                  )}
                </Button>

                {/* User Dropdown Menu */}
                <AnimatePresence>
                  {isUserMenuOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="absolute right-0 mt-2 w-48 bg-card border border-border rounded-md shadow-lg z-50"
                    >
                      <div className="py-1">
                        <div className="px-4 py-2 border-b border-border">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-foreground">{user.name}</p>
                              <p className="text-xs text-muted-foreground">{user.email}</p>
                            </div>
                            <Badge
                              variant={user.role === 'admin' ? 'default' : 'secondary'}
                              className={user.role === 'admin' ? 'bg-accent text-accent-foreground' : ''}
                            >
                              {user.role === 'admin' ? 'Admin' : 'User'}
                            </Badge>
                          </div>
                        </div>
                        <Link href="/profile" className="flex items-center space-x-2 px-4 py-2 text-sm text-foreground hover:bg-muted">
                          <UserIcon className="h-4 w-4" />
                          <span>Profile</span>
                        </Link>
                        <Link href="/orders" className="flex items-center space-x-2 px-4 py-2 text-sm text-foreground hover:bg-muted">
                          <ClipboardDocumentListIcon className="h-4 w-4" />
                          <span>My Orders</span>
                        </Link>

                        {/* Admin-specific menu items */}
                        {user.role === 'admin' && (
                          <>
                            <div className="border-t border-border my-1"></div>
                            <div className="px-4 py-1">
                              <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Admin</p>
                            </div>
                            <Link href="/admin" className="flex items-center space-x-2 px-4 py-2 text-sm text-accent hover:bg-accent/10">
                              <ChartBarIcon className="h-4 w-4" />
                              <span>Dashboard</span>
                            </Link>
                            <Link href="/admin/products" className="flex items-center space-x-2 px-4 py-2 text-sm text-accent hover:bg-accent/10">
                              <Cog6ToothIcon className="h-4 w-4" />
                              <span>Manage Products</span>
                            </Link>
                            <Link href="/admin/orders" className="flex items-center space-x-2 px-4 py-2 text-sm text-accent hover:bg-accent/10">
                              <ClipboardDocumentListIcon className="h-4 w-4" />
                              <span>Manage Orders</span>
                            </Link>
                          </>
                        )}

                        <button
                          onClick={logout}
                          className="w-full text-left px-4 py-2 text-sm text-foreground hover:bg-muted border-t border-border"
                        >
                          Sign Out
                        </button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ) : (
              <Link href="/auth/login">
                <Button variant="outline" size="sm">
                  Sign In
                </Button>
              </Link>
            )}

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <XMarkIcon className="h-5 w-5" />
              ) : (
                <Bars3Icon className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>



        {/* Mobile Navigation */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="md:hidden border-t border-border"
            >
              <nav className="py-4 space-y-2">
                {navigation.map((item) => {
                  const Icon = item.icon;
                  const isAdminRoute = item.href.startsWith('/admin');
                  const isActive = pathname === item.href || (item.href !== '/' && pathname.startsWith(item.href));

                  return (
                    <button
                      key={item.name}
                      onClick={() => {
                        handleNavClick(item.href, item.requiresAuth);
                        setIsMobileMenuOpen(false);
                      }}
                      className={`flex items-center space-x-3 w-full text-left px-4 py-3 text-sm font-medium rounded-md transition-all duration-200 ${
                        isActive
                          ? isAdminRoute
                            ? 'bg-accent text-accent-foreground shadow-sm'
                            : 'bg-accent/10 text-accent border border-accent/20'
                          : isAdminRoute
                            ? 'text-accent hover:bg-accent/10 border border-accent/20'
                            : 'text-foreground hover:bg-muted'
                      }`}
                    >
                      <Icon className="h-5 w-5" />
                      <span className="flex-1">{item.name}</span>
                      {isAdminRoute && (
                        <Badge
                          variant={isActive ? "outline" : "secondary"}
                          className={`text-xs ${isActive ? 'border-accent-foreground/20 text-accent-foreground' : ''}`}
                        >
                          Admin
                        </Badge>
                      )}
                    </button>
                  );
                })}

                {isAuthenticated && (
                  <div className="border-t border-border pt-2 mt-2">
                    <Link
                      href="/profile"
                      className="block px-4 py-2 text-sm font-medium text-foreground hover:bg-muted rounded-md transition-colors"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Profile
                    </Link>
                    <Link
                      href="/orders"
                      className="block px-4 py-2 text-sm font-medium text-foreground hover:bg-muted rounded-md transition-colors"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      My Orders
                    </Link>
                    <button
                      onClick={() => {
                        logout();
                        setIsMobileMenuOpen(false);
                      }}
                      className="block w-full text-left px-4 py-2 text-sm font-medium text-foreground hover:bg-muted rounded-md transition-colors"
                    >
                      Sign Out
                    </button>
                  </div>
                )}
              </nav>
            </motion.div>
          )}
        </AnimatePresence>
        </div>
      </header>
    </div>
  );
}
