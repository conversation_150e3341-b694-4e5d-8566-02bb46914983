'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  MinusIcon, 
  PlusIcon, 
  TrashIcon 
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { formatPrice } from '@/lib/utils';
import { CartItem as CartItemType } from '@/types';

interface CartItemProps {
  item: CartItemType;
  onUpdateQuantity: (itemId: string, quantity: number) => void;
  onRemove: (itemId: string) => void;
}

export function CartItem({ item, onUpdateQuantity, onRemove }: CartItemProps) {
  const { product, variant, quantity } = item;
  const totalPrice = variant.price * quantity;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="flex gap-4 p-4 border border-border rounded-lg bg-card"
    >
      {/* Product Image */}
      <div className="relative w-20 h-20 flex-shrink-0 rounded-md overflow-hidden bg-muted">
        {product.images.length > 0 ? (
          <Image
            src={product.images[0]}
            alt={product.name}
            fill
            className="object-cover"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-accent/20 to-highlight/20 flex items-center justify-center">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-xs">B</span>
            </div>
          </div>
        )}
      </div>

      {/* Product Details */}
      <div className="flex-1 min-w-0">
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
          <div className="flex-1 min-w-0">
            <Link 
              href={`/products/${product.id}`}
              className="font-semibold text-foreground hover:text-accent transition-colors line-clamp-2"
            >
              {product.name}
            </Link>
            
            {/* Variant Details */}
            <div className="flex flex-wrap gap-1 mt-2">
              <Badge variant="outline" className="text-xs">
                {variant.size}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {variant.bedazzlingLevel}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {variant.frameOption}
              </Badge>
              {variant.complexity && (
                <Badge variant="outline" className="text-xs">
                  {variant.complexity}
                </Badge>
              )}
            </div>

            {/* Stock Status */}
            <div className="mt-2">
              {variant.stock > 0 ? (
                <Badge variant="success" className="text-xs">
                  In Stock ({variant.stock} available)
                </Badge>
              ) : (
                <Badge variant="destructive" className="text-xs">
                  Out of Stock
                </Badge>
              )}
            </div>
          </div>

          {/* Price */}
          <div className="text-right">
            <div className="font-bold text-lg text-foreground">
              {formatPrice(totalPrice)}
            </div>
            <div className="text-sm text-muted-foreground">
              {formatPrice(variant.price)} each
            </div>
          </div>
        </div>

        {/* Quantity Controls and Remove */}
        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Quantity:</span>
            <div className="flex items-center border border-border rounded-md">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onUpdateQuantity(item.id, Math.max(1, quantity - 1))}
                disabled={quantity <= 1}
                className="h-8 w-8 p-0"
              >
                <MinusIcon className="h-3 w-3" />
              </Button>
              <span className="px-3 py-1 text-sm font-medium min-w-[2rem] text-center">
                {quantity}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onUpdateQuantity(item.id, quantity + 1)}
                disabled={quantity >= variant.stock}
                className="h-8 w-8 p-0"
              >
                <PlusIcon className="h-3 w-3" />
              </Button>
            </div>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => onRemove(item.id)}
            className="text-destructive hover:text-destructive hover:bg-destructive/10"
          >
            <TrashIcon className="h-4 w-4 mr-1" />
            Remove
          </Button>
        </div>
      </div>
    </motion.div>
  );
}
