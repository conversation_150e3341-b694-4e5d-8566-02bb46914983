'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { CheckIcon } from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  KENYAN_PAYMENT_METHODS, 
  getAvailablePaymentMethods, 
  calculateProcessingFee,
  formatKES 
} from '@/data/payment-methods';

interface PaymentMethodsProps {
  orderAmount: number;
  selectedMethod?: string;
  onMethodSelect: (methodId: string) => void;
  onPaymentSubmit: (paymentData: any) => void;
}

export function PaymentMethods({ 
  orderAmount, 
  selectedMethod, 
  onMethodSelect, 
  onPaymentSubmit 
}: PaymentMethodsProps) {
  const [paymentData, setPaymentData] = useState<any>({});
  const [isProcessing, setIsProcessing] = useState(false);

  const availableMethods = getAvailablePaymentMethods(orderAmount);
  const selectedPaymentMethod = availableMethods.find(m => m.id === selectedMethod);

  const handlePaymentSubmit = async () => {
    if (!selectedMethod) return;
    
    setIsProcessing(true);
    try {
      await onPaymentSubmit({
        method: selectedMethod,
        amount: orderAmount,
        processingFee: calculateProcessingFee(orderAmount, selectedMethod),
        data: paymentData
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const renderPaymentForm = () => {
    if (!selectedPaymentMethod) return null;

    switch (selectedPaymentMethod.id) {
      case 'mpesa':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">M-Pesa Phone Number</label>
              <Input
                type="tel"
                placeholder="**********"
                value={paymentData.phoneNumber || ''}
                onChange={(e) => setPaymentData({ ...paymentData, phoneNumber: e.target.value })}
                className="w-full"
              />
              <p className="text-xs text-muted-foreground mt-1">
                You will receive an M-Pesa prompt on this number
              </p>
            </div>
          </div>
        );

      case 'airtel_money':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Airtel Money Phone Number</label>
              <Input
                type="tel"
                placeholder="**********"
                value={paymentData.phoneNumber || ''}
                onChange={(e) => setPaymentData({ ...paymentData, phoneNumber: e.target.value })}
                className="w-full"
              />
            </div>
          </div>
        );

      case 'bank_transfer':
        return (
          <div className="space-y-4">
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">Bank Details</h4>
              <div className="space-y-1 text-sm">
                <p><strong>Bank:</strong> Equity Bank Kenya</p>
                <p><strong>Account Name:</strong> Bedazzled Kenya Ltd</p>
                <p><strong>Account Number:</strong> **********</p>
                <p><strong>Branch:</strong> Westlands Branch</p>
                <p><strong>Reference:</strong> Your Order ID</p>
              </div>
            </div>
            <p className="text-sm text-muted-foreground">
              Please use your order ID as the reference when making the transfer.
            </p>
          </div>
        );

      case 'card_payment':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Card Number</label>
                <Input
                  type="text"
                  placeholder="1234 5678 9012 3456"
                  value={paymentData.cardNumber || ''}
                  onChange={(e) => setPaymentData({ ...paymentData, cardNumber: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Expiry Date</label>
                  <Input
                    type="text"
                    placeholder="MM/YY"
                    value={paymentData.expiryDate || ''}
                    onChange={(e) => setPaymentData({ ...paymentData, expiryDate: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">CVV</label>
                  <Input
                    type="text"
                    placeholder="123"
                    value={paymentData.cvv || ''}
                    onChange={(e) => setPaymentData({ ...paymentData, cvv: e.target.value })}
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 'cash_on_delivery':
        return (
          <div className="space-y-4">
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">Cash on Delivery</h4>
              <p className="text-sm text-muted-foreground">
                You will pay cash when your order is delivered. Available only in Nairobi.
                Please ensure you have the exact amount ready.
              </p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Choose Payment Method</h3>
        <div className="grid grid-cols-1 gap-3">
          {availableMethods.map((method) => {
            const processingFee = calculateProcessingFee(orderAmount, method.id);
            const isSelected = selectedMethod === method.id;

            return (
              <motion.div
                key={method.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Card 
                  className={`cursor-pointer transition-all ${
                    isSelected ? 'ring-2 ring-primary border-primary' : 'hover:border-primary/50'
                  }`}
                  onClick={() => onMethodSelect(method.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="text-2xl">{method.icon}</div>
                        <div>
                          <h4 className="font-medium">{method.name}</h4>
                          <p className="text-sm text-muted-foreground">{method.description}</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {method.estimatedProcessingTime}
                            </Badge>
                            {processingFee > 0 && (
                              <Badge variant="secondary" className="text-xs">
                                +{formatKES(processingFee)} fee
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      {isSelected && (
                        <CheckIcon className="h-5 w-5 text-primary" />
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>

      {selectedPaymentMethod && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Payment Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {renderPaymentForm()}
              
              <div className="border-t pt-4">
                <div className="flex justify-between items-center mb-2">
                  <span>Order Amount:</span>
                  <span>{formatKES(orderAmount)}</span>
                </div>
                {calculateProcessingFee(orderAmount, selectedPaymentMethod.id) > 0 && (
                  <div className="flex justify-between items-center mb-2">
                    <span>Processing Fee:</span>
                    <span>{formatKES(calculateProcessingFee(orderAmount, selectedPaymentMethod.id))}</span>
                  </div>
                )}
                <div className="flex justify-between items-center font-semibold text-lg border-t pt-2">
                  <span>Total:</span>
                  <span>{formatKES(orderAmount + calculateProcessingFee(orderAmount, selectedPaymentMethod.id))}</span>
                </div>
              </div>

              <Button 
                onClick={handlePaymentSubmit}
                disabled={isProcessing}
                className="w-full"
                size="lg"
              >
                {isProcessing ? 'Processing...' : `Pay ${formatKES(orderAmount + calculateProcessingFee(orderAmount, selectedPaymentMethod.id))}`}
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
}
