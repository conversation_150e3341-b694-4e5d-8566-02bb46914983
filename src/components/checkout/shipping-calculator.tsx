'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { TruckIcon, MapPinIcon, ClockIcon } from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  KENYAN_SHIPPING_ZONES, 
  calculateShippingCost, 
  getShippingZoneByArea,
  formatKES 
} from '@/data/payment-methods';

interface ShippingCalculatorProps {
  orderAmount: number;
  onShippingSelect: (zoneId: string, cost: number) => void;
  selectedZone?: string;
}

export function ShippingCalculator({ 
  orderAmount, 
  onShippingSelect, 
  selectedZone 
}: ShippingCalculatorProps) {
  const [searchArea, setSearchArea] = useState('');
  const [suggestedZones, setSuggestedZones] = useState(KENYAN_SHIPPING_ZONES);
  const [selectedZoneData, setSelectedZoneData] = useState<any>(null);

  useEffect(() => {
    if (searchArea.trim()) {
      const filtered = KENYAN_SHIPPING_ZONES.filter(zone =>
        zone.name.toLowerCase().includes(searchArea.toLowerCase()) ||
        zone.areas.some(area => 
          area.toLowerCase().includes(searchArea.toLowerCase())
        )
      );
      setSuggestedZones(filtered);
    } else {
      setSuggestedZones(KENYAN_SHIPPING_ZONES);
    }
  }, [searchArea]);

  useEffect(() => {
    if (selectedZone) {
      const zone = KENYAN_SHIPPING_ZONES.find(z => z.id === selectedZone);
      setSelectedZoneData(zone);
    }
  }, [selectedZone]);

  const handleZoneSelect = (zone: any) => {
    const shippingCost = calculateShippingCost(zone.id, orderAmount);
    onShippingSelect(zone.id, shippingCost);
    setSelectedZoneData(zone);
  };

  const getDeliveryBadgeColor = (days: string) => {
    if (days.includes('Same day')) return 'success';
    if (days.includes('1-2')) return 'default';
    if (days.includes('3-5')) return 'secondary';
    return 'outline';
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Shipping Information</h3>
        
        {/* Area Search */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">
            Search your area or select a shipping zone
          </label>
          <div className="relative">
            <MapPinIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="e.g., Westlands, Nairobi, Mombasa..."
              value={searchArea}
              onChange={(e) => setSearchArea(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Shipping Zones */}
        <div className="space-y-3">
          {suggestedZones.map((zone) => {
            const shippingCost = calculateShippingCost(zone.id, orderAmount);
            const isSelected = selectedZone === zone.id;
            const isFreeShipping = shippingCost === 0;

            return (
              <motion.div
                key={zone.id}
                whileHover={{ scale: 1.01 }}
                whileTap={{ scale: 0.99 }}
              >
                <Card 
                  className={`cursor-pointer transition-all ${
                    isSelected ? 'ring-2 ring-primary border-primary' : 'hover:border-primary/50'
                  }`}
                  onClick={() => handleZoneSelect(zone)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="font-medium">{zone.name}</h4>
                          <Badge 
                            variant={getDeliveryBadgeColor(zone.estimatedDeliveryDays)}
                            className="text-xs"
                          >
                            <ClockIcon className="h-3 w-3 mr-1" />
                            {zone.estimatedDeliveryDays}
                          </Badge>
                        </div>
                        
                        <div className="text-sm text-muted-foreground mb-2">
                          <strong>Areas:</strong> {zone.areas.slice(0, 3).join(', ')}
                          {zone.areas.length > 3 && ` +${zone.areas.length - 3} more`}
                        </div>

                        {zone.freeShippingThreshold && orderAmount < zone.freeShippingThreshold && (
                          <div className="text-xs text-muted-foreground">
                            Free shipping on orders above {formatKES(zone.freeShippingThreshold)}
                          </div>
                        )}
                      </div>

                      <div className="text-right">
                        <div className="text-lg font-semibold">
                          {isFreeShipping ? (
                            <Badge variant="success" className="text-sm">
                              <TruckIcon className="h-3 w-3 mr-1" />
                              FREE
                            </Badge>
                          ) : (
                            <span>{formatKES(shippingCost)}</span>
                          )}
                        </div>
                        {zone.cost > 0 && shippingCost === 0 && (
                          <div className="text-xs text-muted-foreground line-through">
                            {formatKES(zone.cost)}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Selected Shipping Summary */}
      {selectedZoneData && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
        >
          <Card className="bg-muted/50">
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <TruckIcon className="h-5 w-5 mr-2" />
                Shipping Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span>Delivery Zone:</span>
                <span className="font-medium">{selectedZoneData.name}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span>Estimated Delivery:</span>
                <Badge variant={getDeliveryBadgeColor(selectedZoneData.estimatedDeliveryDays)}>
                  {selectedZoneData.estimatedDeliveryDays}
                </Badge>
              </div>
              
              <div className="flex justify-between items-center">
                <span>Shipping Cost:</span>
                <span className="font-medium">
                  {calculateShippingCost(selectedZoneData.id, orderAmount) === 0 ? (
                    <Badge variant="success">FREE</Badge>
                  ) : (
                    formatKES(calculateShippingCost(selectedZoneData.id, orderAmount))
                  )}
                </span>
              </div>

              {selectedZoneData.freeShippingThreshold && 
               orderAmount < selectedZoneData.freeShippingThreshold && (
                <div className="text-sm text-muted-foreground bg-background p-3 rounded-md">
                  💡 <strong>Tip:</strong> Add {formatKES(selectedZoneData.freeShippingThreshold - orderAmount)} more to get free shipping!
                </div>
              )}

              <div className="text-xs text-muted-foreground">
                <strong>Delivery areas include:</strong> {selectedZoneData.areas.join(', ')}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Special Notes */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <h4 className="font-medium text-blue-900 mb-2">Delivery Information</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• All deliveries are made Monday to Saturday, 8 AM - 6 PM</li>
            <li>• Same-day delivery available for orders placed before 2 PM</li>
            <li>• Remote area deliveries may take longer due to logistics</li>
            <li>• We'll call you before delivery to confirm availability</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
