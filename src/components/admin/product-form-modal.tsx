'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  PlusIcon,
  MinusIcon,
  PhotoIcon,
  CheckIcon,
  TagIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ImageUpload } from '@/components/custom-orders/image-upload';

interface ProductVariant {
  id: string;
  size: string;
  bedazzlingLevel: string;
  frameOption: string;
  price: number;
  stock: number;
}

interface ProductFormData {
  name: string;
  description: string;
  category: string;
  images: File[];
  variants: ProductVariant[];
  basePrice: number;
  featured: boolean;
  tags: string[];
  sku?: string;
  metaTitle?: string;
  metaDescription?: string;
}

interface ProductFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: ProductFormData) => Promise<void>;
  product?: any; // For editing existing products
  isLoading?: boolean;
}

export function ProductFormModal({ 
  isOpen, 
  onClose, 
  onSubmit, 
  product, 
  isLoading = false 
}: ProductFormModalProps) {
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    category: 'pre-made',
    images: [],
    variants: [],
    basePrice: 0,
    featured: false,
    tags: [],
    sku: '',
    metaTitle: '',
    metaDescription: ''
  });
  const [newTag, setNewTag] = useState('');
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name || '',
        description: product.description || '',
        category: product.category || 'pre-made',
        images: [], // Will be handled separately for existing products
        variants: product.variants || [],
        basePrice: product.basePrice || 0,
        featured: product.featured || false,
        tags: product.tags || [],
        sku: product.sku || '',
        metaTitle: product.metaTitle || '',
        metaDescription: product.metaDescription || ''
      });
    } else {
      // Reset form for new product with a default variant
      const defaultVariant: ProductVariant = {
        id: `variant-${Date.now()}`,
        size: 'medium',
        bedazzlingLevel: 'medium',
        frameOption: 'premium',
        price: 0,
        stock: 0
      };

      setFormData({
        name: '',
        description: '',
        category: 'pre-made',
        images: [],
        variants: [defaultVariant],
        basePrice: 0,
        featured: false,
        tags: [],
        sku: '',
        metaTitle: '',
        metaDescription: ''
      });
    }
  }, [product, isOpen]);

  const handleInputChange = (field: keyof ProductFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addVariant = () => {
    const newVariant: ProductVariant = {
      id: `variant-${Date.now()}`,
      size: 'medium',
      bedazzlingLevel: 'medium',
      frameOption: 'premium',
      price: formData.basePrice,
      stock: 0
    };
    setFormData(prev => ({
      ...prev,
      variants: [...prev.variants, newVariant]
    }));
  };

  const updateVariant = (index: number, field: keyof ProductVariant, value: any) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants.map((variant, i) => 
        i === index ? { ...variant, [field]: value } : variant
      )
    }));
  };

  const removeVariant = (index: number) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants.filter((_, i) => i !== index)
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (submitting) return;

    setSubmitting(true);
    try {
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-background rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        >
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold">
                {product ? 'Edit Product' : 'Create New Product'}
              </h2>
              <Button variant="ghost" onClick={onClose}>
                <XMarkIcon className="h-5 w-5" />
              </Button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Product Name *
                      </label>
                      <Input
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="Enter product name"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Category *
                      </label>
                      <select
                        value={formData.category}
                        onChange={(e) => handleInputChange('category', e.target.value)}
                        className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground"
                        required
                      >
                        <option value="pre-made">Pre-made</option>
                        <option value="custom">Custom</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Description *
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="Enter product description"
                      className="w-full min-h-[100px] px-3 py-2 border border-border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-accent"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Base Price (KES) *
                      </label>
                      <Input
                        type="number"
                        value={formData.basePrice}
                        onChange={(e) => {
                          const newPrice = Number(e.target.value);
                          handleInputChange('basePrice', newPrice);
                          // Update first variant price if it exists
                          if (formData.variants.length > 0) {
                            updateVariant(0, 'price', newPrice);
                          }
                        }}
                        placeholder="0"
                        min="0"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        SKU
                      </label>
                      <Input
                        value={formData.sku}
                        onChange={(e) => handleInputChange('sku', e.target.value)}
                        placeholder="Product SKU"
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="featured"
                      checked={formData.featured}
                      onChange={(e) => handleInputChange('featured', e.target.checked)}
                      className="rounded border-border"
                    />
                    <label htmlFor="featured" className="text-sm font-medium text-foreground">
                      Featured Product
                    </label>
                  </div>
                </CardContent>
              </Card>

              {/* Images */}
              <Card>
                <CardHeader>
                  <CardTitle>Product Images</CardTitle>
                  <CardDescription>
                    Upload high-quality images of your product
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ImageUpload
                    images={formData.images}
                    onImagesChange={(images) => handleInputChange('images', images)}
                    maxImages={10}
                    maxSizePerImage={5}
                  />
                </CardContent>
              </Card>

              {/* Product Variants */}
              <Card>
                <CardHeader>
                  <CardTitle>Product Variants</CardTitle>
                  <CardDescription>
                    Define different size, bedazzling, and frame options for your product
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Variants ({formData.variants.length})</span>
                    <Button type="button" onClick={addVariant} variant="outline" size="sm">
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Add Variant
                    </Button>
                  </div>

                  {formData.variants.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <p>No variants added yet. Add at least one variant to continue.</p>
                    </div>
                  )}

                  <div className="space-y-4">
                    {formData.variants.map((variant, index) => (
                      <div key={variant.id} className="border border-border rounded-lg p-4 space-y-4">
                        <div className="flex justify-between items-center">
                          <h4 className="font-medium">Variant {index + 1}</h4>
                          <Button
                            type="button"
                            onClick={() => removeVariant(index)}
                            variant="destructive"
                            size="sm"
                          >
                            <MinusIcon className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-foreground mb-2">
                              Size
                            </label>
                            <select
                              value={variant.size}
                              onChange={(e) => updateVariant(index, 'size', e.target.value)}
                              className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground"
                            >
                              <option value="small">Small</option>
                              <option value="medium">Medium</option>
                              <option value="large">Large</option>
                              <option value="extra-large">Extra Large</option>
                            </select>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-foreground mb-2">
                              Bedazzling Level
                            </label>
                            <select
                              value={variant.bedazzlingLevel}
                              onChange={(e) => updateVariant(index, 'bedazzlingLevel', e.target.value)}
                              className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground"
                            >
                              <option value="light">Light</option>
                              <option value="medium">Medium</option>
                              <option value="heavy">Heavy</option>
                              <option value="premium">Premium</option>
                            </select>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-foreground mb-2">
                              Frame Option
                            </label>
                            <select
                              value={variant.frameOption}
                              onChange={(e) => updateVariant(index, 'frameOption', e.target.value)}
                              className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground"
                            >
                              <option value="basic">Basic</option>
                              <option value="premium">Premium</option>
                              <option value="luxury">Luxury</option>
                            </select>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-foreground mb-2">
                              Price (KES)
                            </label>
                            <Input
                              type="number"
                              value={variant.price}
                              onChange={(e) => updateVariant(index, 'price', Number(e.target.value))}
                              placeholder="0"
                              min="0"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-foreground mb-2">
                              Stock
                            </label>
                            <Input
                              type="number"
                              value={variant.stock}
                              onChange={(e) => updateVariant(index, 'stock', Number(e.target.value))}
                              placeholder="0"
                              min="0"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Tags */}
              <Card>
                <CardHeader>
                  <CardTitle>Tags</CardTitle>
                  <CardDescription>
                    Add tags to help customers find your product
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <Input
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      placeholder="Add a tag"
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    />
                    <Button type="button" onClick={addTag} variant="outline">
                      <PlusIcon className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        <TagIcon className="h-3 w-3" />
                        {tag}
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="ml-1 hover:text-destructive"
                        >
                          <XMarkIcon className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Submit Button */}
              <div className="flex justify-end gap-4">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={submitting || isLoading || formData.variants.length === 0}
                >
                  {submitting ? (
                    'Saving...'
                  ) : (
                    <>
                      <CheckIcon className="h-4 w-4 mr-2" />
                      {product ? 'Update Product' : 'Create Product'}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}
