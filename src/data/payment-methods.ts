/**
 * Payment methods available in Kenya
 * Configured for the Kenyan market with local payment preferences
 */

export interface PaymentMethod {
  id: string;
  name: string;
  description: string;
  icon: string;
  enabled: boolean;
  processingFee: number; // Percentage
  minimumAmount: number; // KES
  maximumAmount: number; // KES
  estimatedProcessingTime: string;
  supportedCurrencies: string[];
}

export const KENYAN_PAYMENT_METHODS: PaymentMethod[] = [
  {
    id: 'mpesa',
    name: 'M-<PERSON><PERSON><PERSON>',
    description: 'Pay securely with M-Pesa mobile money',
    icon: '📱',
    enabled: true,
    processingFee: 0, // No additional fee for M-Pesa
    minimumAmount: 100, // KES 100
    maximumAmount: 300000, // KES 300,000 (daily limit)
    estimatedProcessingTime: 'Instant',
    supportedCurrencies: ['KES']
  },
  {
    id: 'airtel_money',
    name: 'Airtel Money',
    description: 'Pay with Airtel Money mobile wallet',
    icon: '💰',
    enabled: true,
    processingFee: 0,
    minimumAmount: 100,
    maximumAmount: 150000, // KES 150,000
    estimatedProcessingTime: 'Instant',
    supportedCurrencies: ['KES']
  },
  {
    id: 'bank_transfer',
    name: 'Bank Transfer',
    description: 'Direct bank transfer to our account',
    icon: '🏦',
    enabled: true,
    processingFee: 0,
    minimumAmount: 500,
    maximumAmount: 1000000, // KES 1M
    estimatedProcessingTime: '1-2 business days',
    supportedCurrencies: ['KES']
  },
  {
    id: 'card_payment',
    name: 'Debit/Credit Card',
    description: 'Pay with Visa, Mastercard, or local bank cards',
    icon: '💳',
    enabled: true,
    processingFee: 2.5, // 2.5% processing fee
    minimumAmount: 100,
    maximumAmount: 500000,
    estimatedProcessingTime: 'Instant',
    supportedCurrencies: ['KES', 'USD']
  },
  {
    id: 'cash_on_delivery',
    name: 'Cash on Delivery',
    description: 'Pay cash when your order is delivered (Nairobi only)',
    icon: '💵',
    enabled: true,
    processingFee: 0,
    minimumAmount: 1000,
    maximumAmount: 50000,
    estimatedProcessingTime: 'On delivery',
    supportedCurrencies: ['KES']
  }
];

/**
 * Shipping zones and costs for Kenya
 */
export interface ShippingZone {
  id: string;
  name: string;
  areas: string[];
  cost: number; // KES
  estimatedDeliveryDays: string;
  freeShippingThreshold?: number; // KES
}

export const KENYAN_SHIPPING_ZONES: ShippingZone[] = [
  {
    id: 'nairobi_cbd',
    name: 'Nairobi CBD',
    areas: [
      'Central Business District',
      'Westlands',
      'Kilimani',
      'Lavington',
      'Karen',
      'Runda',
      'Muthaiga'
    ],
    cost: 0, // Free delivery in premium areas
    estimatedDeliveryDays: 'Same day',
    freeShippingThreshold: 0
  },
  {
    id: 'nairobi_suburbs',
    name: 'Nairobi Suburbs',
    areas: [
      'Kasarani',
      'Embakasi',
      'Dagoretti',
      'Kibra',
      'Mathare',
      'Roysambu',
      'Ruaraka'
    ],
    cost: 300, // KES 300
    estimatedDeliveryDays: '1-2 days',
    freeShippingThreshold: 15000 // Free shipping above KES 15,000
  },
  {
    id: 'kiambu_county',
    name: 'Kiambu County',
    areas: [
      'Thika',
      'Ruiru',
      'Juja',
      'Limuru',
      'Kikuyu',
      'Kiambu Town'
    ],
    cost: 500, // KES 500
    estimatedDeliveryDays: '2-3 days',
    freeShippingThreshold: 20000
  },
  {
    id: 'central_kenya',
    name: 'Central Kenya',
    areas: [
      'Nyeri',
      'Muranga',
      'Kirinyaga',
      'Nyandarua',
      'Nakuru',
      'Naivasha'
    ],
    cost: 800, // KES 800
    estimatedDeliveryDays: '3-5 days',
    freeShippingThreshold: 25000
  },
  {
    id: 'major_towns',
    name: 'Major Towns',
    areas: [
      'Mombasa',
      'Kisumu',
      'Eldoret',
      'Thika',
      'Machakos',
      'Meru',
      'Embu',
      'Kitale'
    ],
    cost: 1200, // KES 1,200
    estimatedDeliveryDays: '4-7 days',
    freeShippingThreshold: 30000
  },
  {
    id: 'remote_areas',
    name: 'Remote Areas',
    areas: [
      'Northern Kenya',
      'Coastal regions (excluding Mombasa)',
      'Western Kenya (rural)',
      'Eastern Kenya (rural)',
      'Other remote locations'
    ],
    cost: 2000, // KES 2,000
    estimatedDeliveryDays: '7-14 days',
    freeShippingThreshold: 40000
  }
];

/**
 * Get available payment methods for a given amount
 */
export function getAvailablePaymentMethods(amount: number): PaymentMethod[] {
  return KENYAN_PAYMENT_METHODS.filter(method => 
    method.enabled && 
    amount >= method.minimumAmount && 
    amount <= method.maximumAmount
  );
}

/**
 * Calculate shipping cost for a given zone and order amount
 */
export function calculateShippingCost(zoneId: string, orderAmount: number): number {
  const zone = KENYAN_SHIPPING_ZONES.find(z => z.id === zoneId);
  if (!zone) return 0;
  
  if (zone.freeShippingThreshold && orderAmount >= zone.freeShippingThreshold) {
    return 0;
  }
  
  return zone.cost;
}

/**
 * Get shipping zone by area name
 */
export function getShippingZoneByArea(area: string): ShippingZone | undefined {
  return KENYAN_SHIPPING_ZONES.find(zone => 
    zone.areas.some(zoneArea => 
      zoneArea.toLowerCase().includes(area.toLowerCase()) ||
      area.toLowerCase().includes(zoneArea.toLowerCase())
    )
  );
}

/**
 * Calculate total payment processing fee
 */
export function calculateProcessingFee(amount: number, paymentMethodId: string): number {
  const method = KENYAN_PAYMENT_METHODS.find(m => m.id === paymentMethodId);
  if (!method) return 0;
  
  return Math.round(amount * (method.processingFee / 100));
}

/**
 * Format KES currency
 */
export function formatKES(amount: number): string {
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
}
