// User types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: 'buyer' | 'admin';
  createdAt: Date;
  updatedAt: Date;
}

// Product types
export type ProductCategory = 'pre-made' | 'custom';
export type ProductSize = 'small' | 'medium' | 'large' | 'extra-large';
export type BedazzlingLevel = 'light' | 'medium' | 'heavy' | 'premium';
export type FrameOption = 'basic' | 'premium' | 'luxury';
export type PortraitComplexity = 'simple' | 'detailed' | 'intricate';

export interface ProductVariant {
  id: string;
  size: ProductSize;
  bedazzlingLevel: BedazzlingLevel;
  frameOption: FrameOption;
  complexity?: PortraitComplexity;
  price: number;
  stock: number;
}

export interface Product {
  id: string;
  name: string;
  description: string;
  category: ProductCategory;
  images: string[];
  variants: ProductVariant[];
  basePrice: number;
  featured: boolean;
  tags: string[];
  rating: number;
  reviewCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// Cart types
export interface CartItem {
  id: string;
  productId: string;
  quantity: number;
  product: Product;
}

export interface Cart {
  id: string;
  userId?: string;
  items: CartItem[];
  total: number;
  createdAt: Date;
  updatedAt: Date;
}

// Order types
export type OrderStatus = 
  | 'pending' 
  | 'confirmed' 
  | 'processing' 
  | 'shipped' 
  | 'delivered' 
  | 'cancelled';

export interface ShippingAddress {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface OrderItem {
  id: string;
  productId: string;
  variantId: string;
  quantity: number;
  price: number;
  product: Product;
  variant: ProductVariant;
}

export interface Order {
  id: string;
  userId: string;
  orderNumber: string;
  items: OrderItem[];
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  status: OrderStatus;
  shippingAddress: ShippingAddress;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Custom order types
export interface CustomOrderRequest {
  id: string;
  userId: string;
  customerName: string;
  email: string;
  description: string;
  referenceImages: string[];
  size: ProductSize;
  budget: number;
  status: 'pending' | 'quoted' | 'approved' | 'in-progress' | 'completed' | 'cancelled';
  quote?: number;
  createdAt: Date;
  updatedAt: Date;
}

// Review types
export interface Review {
  id: string;
  userId: string;
  productId: string;
  orderId: string;
  rating: number;
  title: string;
  comment: string;
  images?: string[];
  verified: boolean;
  helpful: number;
  createdAt: Date;
  updatedAt: Date;
  user: Pick<User, 'name' | 'avatar'>;
}



// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Filter and search types
export interface ProductFilters {
  category?: ProductCategory;
}

export interface SearchParams {
  query?: string;
  filters?: ProductFilters;
  sortBy?: 'name' | 'price' | 'rating' | 'newest' | 'popular';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}
