'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  SparklesIcon,
  HeartIcon,
  StarIcon,
  MapPinIcon,
  PhoneIcon,
  EnvelopeIcon,
  ClockIcon,
  TrophyIcon,
  UsersIcon,
  GlobeAltIcon,
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

export default function AboutPage() {
  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'Founder & Master Artisan',
      bio: 'With over 15 years of experience in traditional Kenyan crafts, <PERSON> founded Bedazzled to bring the beauty of rhinestone art to Kenya.',
      image: '/team/grace.jpg',
    },
    {
      name: '<PERSON>',
      role: 'Lead Designer',
      bio: 'A graduate of the University of Nairobi School of Arts, <PERSON> specializes in contemporary portrait design and digital art.',
      image: '/team/david.jpg',
    },
    {
      name: '<PERSON>',
      role: 'Senior Artisan',
      bio: 'Mary brings precision and attention to detail to every piece, ensuring each rhinestone is perfectly placed.',
      image: '/team/mary.jpg',
    },
    {
      name: '<PERSON>',
      role: 'Quality Assurance Manager',
      bio: '<PERSON> ensures every portrait meets our high standards before it reaches our customers.',
      image: '/team/samuel.jpg',
    },
  ];

  const milestones = [
    { year: '2018', event: 'Founded Bedazzled in Nairobi with a small workshop in Westlands' },
    { year: '2019', event: 'Completed first 100 custom portraits for Kenyan families' },
    { year: '2020', event: 'Expanded to serve customers across East Africa' },
    { year: '2021', event: 'Launched online platform and introduced M-Pesa payments' },
    { year: '2022', event: 'Opened second workshop in Mombasa' },
    { year: '2023', event: 'Reached 1,000+ satisfied customers milestone' },
    { year: '2024', event: 'Introduced premium rhinestone collection and express delivery' },
  ];

  const values = [
    {
      icon: SparklesIcon,
      title: 'Quality Craftsmanship',
      description: 'Every piece is handcrafted with precision and care by our skilled Kenyan artisans.',
    },
    {
      icon: HeartIcon,
      title: 'Customer-Centric',
      description: 'We put our customers first, ensuring every portrait captures their precious memories perfectly.',
    },
    {
      icon: StarIcon,
      title: 'Innovation',
      description: 'We continuously innovate our techniques while honoring traditional Kenyan artistic heritage.',
    },
    {
      icon: GlobeAltIcon,
      title: 'Community Impact',
      description: 'We support local artisans and contribute to the growth of Kenya\'s creative economy.',
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-muted/30 to-background">
        <div className="container mx-auto px-4 py-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <Badge variant="secondary" className="mb-6">
              🇰🇪 Proudly Kenyan
            </Badge>
            <h1 className="text-4xl lg:text-6xl font-bold text-foreground mb-6">
              About
              <span className="bg-gradient-to-r from-primary via-accent to-highlight bg-clip-text text-transparent">
                {' '}Bedazzled
              </span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              Kenya's premier destination for custom rhinestone bedazzled portraits. 
              We transform your precious memories into sparkling works of art that last a lifetime.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <div className="flex items-center gap-2 text-muted-foreground">
                <MapPinIcon className="h-5 w-5" />
                <span>Nairobi & Mombasa, Kenya</span>
              </div>
              <div className="flex items-center gap-2 text-muted-foreground">
                <ClockIcon className="h-5 w-5" />
                <span>Est. 2018</span>
              </div>
              <div className="flex items-center gap-2 text-muted-foreground">
                <TrophyIcon className="h-5 w-5" />
                <span>1,000+ Happy Customers</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-6">
                Our Story
              </h2>
              <div className="space-y-4 text-muted-foreground">
                <p>
                  Bedazzled was born from a passion for preserving memories and celebrating 
                  Kenya's rich artistic heritage. Founded in 2018 by Grace Wanjiku, a master 
                  artisan with over 15 years of experience in traditional Kenyan crafts, 
                  our company began as a small workshop in Westlands, Nairobi.
                </p>
                <p>
                  Grace's vision was simple yet powerful: to create a uniquely Kenyan way 
                  of transforming family photos and precious memories into stunning works 
                  of art using premium rhinestones and traditional craftsmanship techniques.
                </p>
                <p>
                  Today, we're proud to be Kenya's leading provider of custom bedazzled 
                  portraits, serving families across East Africa with our handcrafted 
                  masterpieces that combine modern technology with time-honored artisanal skills.
                </p>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="aspect-square rounded-2xl overflow-hidden bg-gradient-to-br from-accent/20 to-highlight/20 p-8">
                <div className="relative h-full w-full rounded-xl bg-white/80 backdrop-blur-sm border border-white/20 flex items-center justify-center">
                  <div className="text-center space-y-4">
                    <div className="w-24 h-24 mx-auto bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
                      <SparklesIcon className="h-12 w-12 text-white" />
                    </div>
                    <p className="text-lg font-semibold text-foreground">
                      Crafting Memories Since 2018
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Over 1,000 families trust us with their precious moments
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Mission & Values */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-6">
              Our Mission & Values
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              We're committed to preserving your most precious memories while supporting 
              Kenya's creative economy and celebrating our rich cultural heritage.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="text-center h-full">
                  <CardHeader>
                    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-accent to-highlight rounded-full flex items-center justify-center">
                      <value.icon className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-xl">{value.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-base">
                      {value.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-6">
              Meet Our Team
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Our talented team of Kenyan artisans and designers brings passion,
              skill, and creativity to every piece we create.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="text-center">
                  <CardContent className="pt-6">
                    <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-xl">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <h3 className="font-semibold text-foreground mb-1">{member.name}</h3>
                    <p className="text-sm text-accent font-medium mb-3">{member.role}</p>
                    <p className="text-sm text-muted-foreground">{member.bio}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Company Milestones */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-6">
              Our Journey
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              From a small workshop in Westlands to serving customers across East Africa,
              here are the key milestones in our journey.
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            <div className="space-y-6">
              {milestones.map((milestone, index) => (
                <motion.div
                  key={milestone.year}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-center gap-6"
                >
                  <div className="flex-shrink-0 w-20 text-right">
                    <Badge variant="secondary" className="text-sm font-bold">
                      {milestone.year}
                    </Badge>
                  </div>
                  <div className="flex-shrink-0 w-4 h-4 bg-accent rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-foreground">{milestone.event}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-6">
              Visit Our Studios
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Come see our artisans at work or get in touch to start your custom order.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPinIcon className="h-5 w-5 text-accent" />
                  Nairobi Studio
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <p className="text-muted-foreground">
                  Westlands Shopping Centre<br />
                  2nd Floor, Suite 204<br />
                  Nairobi, Kenya
                </p>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <PhoneIcon className="h-4 w-4" />
                  <span>+254 700 123 456</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <ClockIcon className="h-4 w-4" />
                  <span>Mon-Sat: 9AM-6PM</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPinIcon className="h-5 w-5 text-accent" />
                  Mombasa Studio
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <p className="text-muted-foreground">
                  Nyali Centre<br />
                  Ground Floor, Shop G12<br />
                  Mombasa, Kenya
                </p>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <PhoneIcon className="h-4 w-4" />
                  <span>+254 700 654 321</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <ClockIcon className="h-4 w-4" />
                  <span>Mon-Sat: 10AM-7PM</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-center mt-12"
          >
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/custom-orders">
                <Button size="lg">
                  Start Custom Order
                </Button>
              </Link>
              <Button variant="outline" size="lg">
                <EnvelopeIcon className="h-4 w-4 mr-2" />
                <EMAIL>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-foreground text-background py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-br from-primary to-accent">
                  <span className="text-sm font-bold text-white">B</span>
                </div>
                <span className="text-xl font-bold">Bedazzled</span>
              </div>
              <p className="text-background/80">
                Kenya's premier destination for custom rhinestone bedazzled portraits.
              </p>
              <div className="flex space-x-4">
                <div className="flex items-center gap-2 text-sm">
                  <MapPinIcon className="h-4 w-4" />
                  <span>Nairobi & Mombasa</span>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Quick Links</h3>
              <ul className="space-y-2 text-background/80">
                <li><Link href="/" className="hover:text-background transition-colors">Home</Link></li>
                <li><Link href="/products" className="hover:text-background transition-colors">Products</Link></li>
                <li><Link href="/custom-orders" className="hover:text-background transition-colors">Custom Orders</Link></li>
                <li><Link href="/about" className="hover:text-background transition-colors">About Us</Link></li>
              </ul>
            </div>

            {/* Services */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Services</h3>
              <ul className="space-y-2 text-background/80">
                <li>Custom Portraits</li>
                <li>Pre-made Collection</li>
                <li>Express Delivery</li>
                <li>Design Consultation</li>
              </ul>
            </div>

            {/* Contact */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Contact</h3>
              <div className="space-y-2 text-background/80">
                <div className="flex items-center gap-2">
                  <PhoneIcon className="h-4 w-4" />
                  <span>+254 700 123 456</span>
                </div>
                <div className="flex items-center gap-2">
                  <EnvelopeIcon className="h-4 w-4" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center gap-2">
                  <ClockIcon className="h-4 w-4" />
                  <span>Mon-Sat: 9AM-6PM</span>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t border-background/20 mt-12 pt-8 text-center text-background/60">
            <p>&copy; 2024 Bedazzled Kenya. All rights reserved. | Proudly Kenyan 🇰🇪</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
