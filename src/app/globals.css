@import "tailwindcss";

:root {
  /* Base colors - clean white foundation with improved contrast */
  --background: #ffffff;
  --foreground: #0f0f0f;    /* Darker for better contrast (21:1 ratio) */

  /* Enhanced rhinestone-inspired color palette */
  --primary: #1a0b2e;       /* Deep purple-black for elegance */
  --primary-light: #2d1b3d; /* Rich dark purple */
  --accent: #7209b7;        /* Vibrant purple (rhinestone sparkle) */
  --accent-light: #9333ea;  /* Bright purple highlight */
  --highlight: #c084fc;     /* Light purple for accents */

  /* Kenyan-inspired accent colors */
  --kenyan-red: #dc2626;    /* Kenya flag red */
  --kenyan-green: #16a34a;  /* Kenya flag green */
  --kenyan-gold: #f59e0b;   /* Golden highlights */

  /* Supporting colors with better contrast */
  --muted: #f8fafc;         /* Slightly cooler light gray */
  --muted-foreground: #64748b; /* Better contrast for muted text */
  --border: #e2e8f0;        /* Cooler border color */
  --card: #ffffff;          /* Card backgrounds */
  --card-foreground: #0f0f0f; /* High contrast card text */

  /* Status colors with improved accessibility */
  --success: #059669;       /* Darker green for better contrast */
  --warning: #d97706;       /* Darker orange for better contrast */
  --error: #dc2626;         /* Darker red for better contrast */
  --info: #0284c7;          /* Blue for informational content */

  /* Interactive states */
  --hover: #f1f5f9;         /* Light hover state */
  --active: #e2e8f0;        /* Active state */
  --focus: #7209b7;         /* Focus ring color */

  /* Shadows and effects */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-light: var(--primary-light);
  --color-accent: var(--accent);
  --color-accent-light: var(--accent-light);
  --color-highlight: var(--highlight);
  --color-kenyan-red: var(--kenyan-red);
  --color-kenyan-green: var(--kenyan-green);
  --color-kenyan-gold: var(--kenyan-gold);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-error: var(--error);
  --color-info: var(--info);
  --color-hover: var(--hover);
  --color-active: var(--active);
  --color-focus: var(--focus);
  --font-sans: ui-sans-serif, system-ui, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, monospace;
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus styles for better accessibility */
*:focus-visible {
  outline: 2px solid var(--focus);
  outline-offset: 2px;
  border-radius: 4px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --foreground: #000000;
    --background: #ffffff;
    --border: #000000;
    --accent: #0000ff;
  }
}

/* Enhanced animations and effects */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(114, 9, 183, 0.4);
  }
  to {
    box-shadow: 0 0 30px rgba(114, 9, 183, 0.6);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes pulse-accent {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-shimmer {
  background: linear-gradient(110deg, transparent 40%, rgba(255, 255, 255, 0.5) 50%, transparent 60%);
  animation: shimmer 2s infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-pulse-accent {
  animation: pulse-accent 2s ease-in-out infinite;
}

.gradient-text {
  background: linear-gradient(135deg, var(--accent), var(--highlight));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.button-glow {
  position: relative;
  overflow: hidden;
}

.button-glow::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.button-glow:hover::before {
  transform: translateX(100%);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--highlight);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-light);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent);
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--accent);
  outline-offset: 2px;
}

/* Utility classes */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Enhanced utility classes */
.text-balance {
  text-wrap: balance;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-pulse-accent {
  animation: pulse-accent 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-sparkle {
  animation: sparkle 3s ease-in-out infinite;
}

/* Enhanced button styles */
.btn-kenyan {
  background: linear-gradient(135deg, var(--kenyan-red), var(--kenyan-green));
  color: white;
  border: none;
  transition: all 0.3s ease;
}

.btn-kenyan:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
}

/* Rhinestone sparkle effect */
.rhinestone-sparkle {
  position: relative;
  overflow: hidden;
}

.rhinestone-sparkle::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 3s infinite;
}

/* Accessibility helpers */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast text */
.text-high-contrast {
  color: var(--foreground);
  font-weight: 600;
}

/* Enhanced card styles */
.card-enhanced {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 12px;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.card-enhanced:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--accent-light);
}
