'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  PaintBrushIcon,
  CheckIcon,
  PhotoIcon,
  SparklesIcon,
  CurrencyDollarIcon,
  InformationCircleIcon,
  UserIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RoleBasedLayout } from '@/components/layout/role-based-layout';
import { useAuth } from '@/contexts/auth-context';
import { formatPrice } from '@/lib/utils';
import { useRouter } from 'next/navigation';

interface CustomOrderForm {
  customerName: string;
  email: string;
  description: string;
  size: string;
  budget: number;
  image: File | null;
}

export default function CustomOrdersPage() {
  const { user } = useAuth();
  const [formData, setFormData] = useState<CustomOrderForm>({
    customerName: user?.name || '',
    email: user?.email || '',
    description: '',
    size: 'medium',
    budget: 0,
    image: null,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (field: keyof CustomOrderForm, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleImageChange = (file: File | null) => {
    setFormData(prev => ({ ...prev, image: file }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isSubmitting) return;

    setIsSubmitting(true);
    try {
      const formDataToSend = new FormData();
      formDataToSend.append('customerName', formData.customerName);
      formDataToSend.append('email', formData.email);
      formDataToSend.append('description', formData.description);
      formDataToSend.append('size', formData.size);
      formDataToSend.append('budget', formData.budget.toString());
      
      if (formData.image) {
        formDataToSend.append('referenceImages', formData.image);
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/custom-orders`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
        body: formDataToSend,
      });

      if (response.ok) {
        setIsSubmitted(true);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to submit order');
      }
    } catch (error) {
      console.error('Error submitting order:', error);
      alert(`Failed to submit order: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <RoleBasedLayout requiredRole="authenticated">
        <div className="container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-md mx-auto text-center"
          >
            <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-accent to-highlight rounded-full flex items-center justify-center">
              <CheckIcon className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-2xl font-bold text-foreground mb-4">
              Order Submitted Successfully!
            </h1>
            <p className="text-muted-foreground mb-8">
              Thank you for your custom order request. We'll review your requirements and get back to you with a quote within 24 hours.
            </p>
            <Button onClick={() => window.location.href = '/orders'}>
              View My Orders
            </Button>
          </motion.div>
        </div>
      </RoleBasedLayout>
    );
  }

  return (
    <RoleBasedLayout requiredRole="authenticated">
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-accent to-highlight rounded-full flex items-center justify-center">
              <PaintBrushIcon className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl lg:text-4xl font-bold text-foreground">
                Custom Order Request
              </h1>
              <p className="text-muted-foreground">
                Create your personalized bedazzled portrait
              </p>
            </div>
          </div>
        </motion.div>

        {/* Order Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="max-w-2xl mx-auto"
        >
          <Card>
            <CardHeader>
              <CardTitle>Order Details</CardTitle>
              <CardDescription>
                Tell us about your custom bedazzled portrait requirements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Contact Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Your Name *
                    </label>
                    <Input
                      value={formData.customerName}
                      onChange={(e) => handleInputChange('customerName', e.target.value)}
                      placeholder="Enter your name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Email Address *
                    </label>
                    <Input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="Enter your email"
                      required
                    />
                  </div>
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Description *
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Describe your custom portrait requirements..."
                    className="w-full min-h-[120px] px-3 py-2 border border-border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-accent"
                    required
                  />
                </div>

                {/* Size and Budget */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Size *
                    </label>
                    <select
                      value={formData.size}
                      onChange={(e) => handleInputChange('size', e.target.value)}
                      className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground"
                      required
                    >
                      <option value="small">Small (8x10 inches)</option>
                      <option value="medium">Medium (11x14 inches)</option>
                      <option value="large">Large (16x20 inches)</option>
                      <option value="extra-large">Extra Large (20x24 inches)</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Budget (KES) *
                    </label>
                    <Input
                      type="number"
                      value={formData.budget}
                      onChange={(e) => handleInputChange('budget', Number(e.target.value))}
                      placeholder="Enter your budget"
                      min="0"
                      required
                    />
                  </div>
                </div>

                {/* Reference Image */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Reference Image
                  </label>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0] || null;
                      handleImageChange(file);
                    }}
                    className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground"
                  />
                  {formData.image && (
                    <div className="mt-2 text-sm text-muted-foreground">
                      Selected: {formData.image.name}
                    </div>
                  )}
                  <p className="text-xs text-muted-foreground mt-1">
                    Upload a reference image for your custom portrait (optional)
                  </p>
                </div>

                {/* Submit Button */}
                <div className="flex justify-end pt-6 border-t border-border">
                  <Button type="submit" disabled={isSubmitting} size="lg">
                    {isSubmitting ? 'Submitting...' : 'Submit Order Request'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </RoleBasedLayout>
  );
}
