'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  TruckIcon,
  EyeIcon,
  DocumentTextIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  UserIcon,
  PhoneIcon,
  EnvelopeIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { formatPrice } from '@/lib/utils';
import { useRequireAdmin } from '@/contexts/auth-context';
import { RoleBasedLayout } from '@/components/layout/role-based-layout';

// Regular product order interface
interface ProductOrder {
  _id: string;
  userId: {
    _id: string;
    name: string;
    email: string;
  };
  customerName: string;
  email: string;
  phone: string;
  items: Array<{
    productId: string;
    productName: string;
    quantity: number;
    price: number;
  }>;
  total: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  shippingAddress: string;
  paymentMethod: string;
  createdAt: string;
  updatedAt: string;
}

// Custom order interface
interface CustomOrder {
  _id: string;
  userId: {
    _id: string;
    name: string;
    email: string;
  };
  customerName: string;
  email: string;
  description: string;
  size: string;
  budget: number;
  quote?: number;
  status: 'pending' | 'quoted' | 'approved' | 'in-progress' | 'completed' | 'cancelled';
  createdAt: string;
  updatedAt: string;
  adminNotes?: string;
  referenceImages?: string[];
}

export default function AdminOrdersPage() {
  const { user, isLoading } = useRequireAdmin();
  const [activeTab, setActiveTab] = useState<'product' | 'custom'>('product');
  const [productOrders, setProductOrders] = useState<ProductOrder[]>([]);
  const [customOrders, setCustomOrders] = useState<CustomOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedOrder, setSelectedOrder] = useState<ProductOrder | CustomOrder | null>(null);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [updatingStatus, setUpdatingStatus] = useState(false);

  useEffect(() => {
    if (user?.role === 'admin') {
      fetchOrders();
    }
  }, [user]);

  const fetchOrders = async () => {
    try {
      // Fetch both product orders and custom orders
      const [productOrdersResponse, customOrdersResponse] = await Promise.all([
        fetch(`${process.env.NEXT_PUBLIC_API_URL}/orders?limit=100`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          },
        }),
        fetch(`${process.env.NEXT_PUBLIC_API_URL}/custom-orders?limit=100`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          },
        })
      ]);

      if (productOrdersResponse.ok) {
        const productData = await productOrdersResponse.json();
        setProductOrders(productData.data || []);
      } else {
        console.error('Failed to fetch product orders');
        setProductOrders([]);
      }

      if (customOrdersResponse.ok) {
        const customData = await customOrdersResponse.json();
        setCustomOrders(customData.data || []);
      } else {
        console.error('Failed to fetch custom orders');
        setCustomOrders([]);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      setProductOrders([]);
      setCustomOrders([]);
    } finally {
      setLoading(false);
    }
  };

  const updateOrderStatus = async (orderId: string, status: string, quote?: number, adminNotes?: string) => {
    setUpdatingStatus(true);
    try {
      const endpoint = activeTab === 'product'
        ? `${process.env.NEXT_PUBLIC_API_URL}/orders/${orderId}/status`
        : `${process.env.NEXT_PUBLIC_API_URL}/custom-orders/${orderId}/status`;

      const response = await fetch(endpoint, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
        body: JSON.stringify({ status, quote, adminNotes }),
      });

      if (response.ok) {
        const updatedOrder = await response.json();

        if (activeTab === 'product') {
          setProductOrders(productOrders.map(order =>
            order._id === orderId ? updatedOrder : order
          ));
        } else {
          setCustomOrders(customOrders.map(order =>
            order._id === orderId ? updatedOrder : order
          ));
        }

        if (selectedOrder && selectedOrder._id === orderId) {
          setSelectedOrder(updatedOrder);
        }
      } else {
        alert('Failed to update order status');
      }
    } catch (error) {
      console.error('Error updating order:', error);
      alert('Error updating order');
    } finally {
      setUpdatingStatus(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-4 w-4" />;
      case 'quoted':
        return <DocumentTextIcon className="h-4 w-4" />;
      case 'approved':
      case 'in-progress':
      case 'processing':
        return <TruckIcon className="h-4 w-4" />;
      case 'shipped':
        return <TruckIcon className="h-4 w-4" />;
      case 'delivered':
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'cancelled':
        return <XCircleIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'secondary';
      case 'quoted':
        return 'outline';
      case 'approved':
      case 'in-progress':
      case 'processing':
        return 'default';
      case 'shipped':
        return 'default';
      case 'delivered':
      case 'completed':
        return 'default';
      case 'cancelled':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  // Get current orders based on active tab
  const currentOrders = activeTab === 'product' ? productOrders : customOrders;

  const filteredOrders = currentOrders.filter(order => {
    const matchesSearch = order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order._id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center animate-pulse">
            <DocumentTextIcon className="h-8 w-8 text-white" />
          </div>
          <p className="text-muted-foreground">Loading admin panel...</p>
        </div>
      </div>
    );
  }

  return (
    <RoleBasedLayout requiredRole="admin">
      <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="mb-8"
      >
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-2">
              Order Management
            </h1>
            <p className="text-muted-foreground">
              Manage product orders and custom order requests
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="secondary">
              Total: {currentOrders.length}
            </Badge>
            <Badge variant="outline">
              Pending: {currentOrders.filter(o => o.status === 'pending').length}
            </Badge>
          </div>
        </div>

        {/* Order Type Tabs */}
        <div className="flex space-x-1 mt-6 bg-muted p-1 rounded-lg">
          <button
            onClick={() => setActiveTab('product')}
            className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'product'
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            Product Orders ({productOrders.length})
          </button>
          <button
            onClick={() => setActiveTab('custom')}
            className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'custom'
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            Custom Orders ({customOrders.length})
          </button>
        </div>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
        className="mb-6"
      >
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search orders by customer name, email, or order ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
              >
                <option value="all">All Statuses</option>
                <option value="pending">Pending</option>
                {activeTab === 'custom' && <option value="quoted">Quoted</option>}
                {activeTab === 'custom' && <option value="approved">Approved</option>}
                {activeTab === 'product' && <option value="processing">Processing</option>}
                {activeTab === 'product' && <option value="shipped">Shipped</option>}
                {activeTab === 'product' && <option value="delivered">Delivered</option>}
                {activeTab === 'custom' && <option value="in-progress">In Progress</option>}
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Orders List */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading orders...</p>
        </div>
      ) : filteredOrders.length === 0 ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="text-center py-12"
        >
          <DocumentTextIcon className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-foreground mb-2">
            {searchTerm || selectedStatus !== 'all' ? 'No Orders Found' : 'No Orders Yet'}
          </h2>
          <p className="text-muted-foreground">
            {searchTerm || selectedStatus !== 'all' 
              ? 'Try adjusting your search or filter criteria'
              : 'Custom orders will appear here when customers place them'
            }
          </p>
        </motion.div>
      ) : (
        <div className="space-y-4">
          {filteredOrders.map((order, index) => (
            <motion.div
              key={order._id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.05 }}
            >
              <Card className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div className="flex-1 space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <h3 className="font-semibold text-foreground">
                            Order #{order._id.slice(-8)}
                          </h3>
                          <Badge variant={getStatusColor(order.status) as any} className="flex items-center gap-1">
                            {getStatusIcon(order.status)}
                            {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <CalendarIcon className="h-4 w-4" />
                          {new Date(order.createdAt).toLocaleDateString()}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="flex items-center gap-2">
                          <UserIcon className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{order.customerName}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <EnvelopeIcon className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{order.email}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <PhoneIcon className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{order.phone}</span>
                        </div>
                      </div>

                      <div className="flex items-center gap-4 text-sm">
                        <span className="text-muted-foreground">Size:</span>
                        <span className="font-medium capitalize">{order.size}</span>
                        <span className="text-muted-foreground">•</span>
                        <span className="text-muted-foreground">Bedazzling:</span>
                        <span className="font-medium capitalize">{order.bedazzlingLevel}</span>
                        <span className="text-muted-foreground">•</span>
                        <span className="text-muted-foreground">Frame:</span>
                        <span className="font-medium capitalize">{order.frameOption}</span>
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                      <div className="text-right">
                        <div className="flex items-center gap-1">
                          <CurrencyDollarIcon className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">
                            {order.quote ? 'Final Quote' : 'Estimated'}
                          </span>
                        </div>
                        <span className="font-bold text-accent text-lg">
                          {formatPrice(order.quote || order.estimatedPrice || order.budget)}
                        </span>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedOrder(order)}
                        >
                          <EyeIcon className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        {order.status === 'pending' && (
                          <Button
                            size="sm"
                            onClick={() => setSelectedOrder(order)}
                          >
                            <PencilIcon className="h-4 w-4 mr-1" />
                            Quote
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}

      {/* Order Details Modal */}
      {selectedOrder && (
        <OrderDetailsModal
          order={selectedOrder}
          onClose={() => setSelectedOrder(null)}
          onUpdateStatus={updateOrderStatus}
          isUpdating={updatingStatus}
        />
      )}
      </div>
    </RoleBasedLayout>
  );
}

// Order Details Modal Component
interface OrderDetailsModalProps {
  order: CustomOrder;
  onClose: () => void;
  onUpdateStatus: (orderId: string, status: string, quote?: number, adminNotes?: string) => void;
  isUpdating: boolean;
}

function OrderDetailsModal({ order, onClose, onUpdateStatus, isUpdating }: OrderDetailsModalProps) {
  const [newStatus, setNewStatus] = useState(order.status);
  const [quote, setQuote] = useState(order.quote?.toString() || '');
  const [adminNotes, setAdminNotes] = useState(order.adminNotes || '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdateStatus(
      order._id,
      newStatus,
      quote ? Number(quote) : undefined,
      adminNotes || undefined
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-4 w-4" />;
      case 'quoted':
        return <DocumentTextIcon className="h-4 w-4" />;
      case 'approved':
      case 'in-progress':
        return <TruckIcon className="h-4 w-4" />;
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'cancelled':
        return <XCircleIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-background rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto"
      >
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">Order Details</h2>
            <Button variant="ghost" onClick={onClose}>
              ×
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Order Information */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Order Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Order ID</label>
                      <p className="font-mono text-sm">{order._id}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Created</label>
                      <p className="text-sm">{new Date(order.createdAt).toLocaleString()}</p>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Customer</label>
                    <div className="space-y-1">
                      <p className="font-medium">{order.customerName}</p>
                      <p className="text-sm text-muted-foreground">{order.email}</p>
                      <p className="text-sm text-muted-foreground">{order.phone}</p>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Description</label>
                    <p className="text-sm mt-1 p-3 bg-muted rounded-lg">{order.description}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Size</label>
                      <p className="capitalize">{order.size}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Bedazzling</label>
                      <p className="capitalize">{order.bedazzlingLevel}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Frame</label>
                      <p className="capitalize">{order.frameOption}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Quantity</label>
                      <p>{order.quantity}</p>
                    </div>
                  </div>

                  {order.rushOrder && (
                    <div>
                      <Badge variant="destructive">Rush Order</Badge>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Reference Images */}
              {order.referenceImages && order.referenceImages.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Reference Images</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      {order.referenceImages.map((image, index) => (
                        <img
                          key={index}
                          src={`${process.env.NEXT_PUBLIC_API_URL}${image}`}
                          alt={`Reference ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg"
                        />
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Status Management */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Status Management</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Order Status
                      </label>
                      <select
                        value={newStatus}
                        onChange={(e) => setNewStatus(e.target.value)}
                        className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground"
                      >
                        <option value="pending">Pending</option>
                        <option value="quoted">Quoted</option>
                        <option value="approved">Approved</option>
                        <option value="in-progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                      </select>
                    </div>

                    {(newStatus === 'quoted' || order.quote) && (
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Quote Amount (KES)
                        </label>
                        <Input
                          type="number"
                          value={quote}
                          onChange={(e) => setQuote(e.target.value)}
                          placeholder="Enter quote amount"
                          min="0"
                        />
                      </div>
                    )}

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Admin Notes
                      </label>
                      <textarea
                        value={adminNotes}
                        onChange={(e) => setAdminNotes(e.target.value)}
                        placeholder="Add notes for the customer..."
                        className="w-full min-h-[100px] px-3 py-2 border border-border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-accent"
                      />
                    </div>

                    <Button type="submit" className="w-full" disabled={isUpdating}>
                      {isUpdating ? 'Updating...' : 'Update Order'}
                    </Button>
                  </form>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Pricing Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Customer Budget:</span>
                    <span className="font-medium">{formatPrice(order.budget)}</span>
                  </div>
                  {order.estimatedPrice && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Estimated Price:</span>
                      <span className="font-medium">{formatPrice(order.estimatedPrice)}</span>
                    </div>
                  )}
                  {order.quote && (
                    <div className="flex justify-between border-t pt-4">
                      <span className="font-medium">Final Quote:</span>
                      <span className="font-bold text-accent text-lg">{formatPrice(order.quote)}</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
