import type { <PERSON><PERSON><PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { AuthProvider } from "@/contexts/auth-context";
import { CartProvider } from "@/contexts/cart-context";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: {
    default: "Bedazzled - Premium Rhinestone Bedazzled Portraits",
    template: "%s | Bedazzled"
  },
  description: "Transform your precious memories into stunning, one-of-a-kind rhinestone artwork. Each piece is meticulously handcrafted by our Kenyan artisans with premium materials.",
  keywords: ["bedazzled", "rhinestone", "portraits", "custom art", "personalized gifts", "kenya", "handcrafted", "premium"],
  authors: [{ name: "Bedazzled Team" }],
  creator: "Bedazzled",
  publisher: "Bedazzled",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://bedazzled.co.ke'),
  alternates: {
    canonical: '/',
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
    apple: '/favicon.ico',
  },
  manifest: '/site.webmanifest',
  openGraph: {
    type: "website",
    locale: "en_KE",
    url: "https://bedazzled.co.ke",
    title: "Bedazzled - Premium Rhinestone Bedazzled Portraits",
    description: "Transform your precious memories into stunning, one-of-a-kind rhinestone artwork. Handcrafted by Kenyan artisans.",
    siteName: "Bedazzled",
    images: [
      {
        url: '/favicon.ico',
        width: 1200,
        height: 630,
        alt: 'Bedazzled - Premium Rhinestone Bedazzled Portraits',
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Bedazzled - Premium Rhinestone Bedazzled Portraits",
    description: "Transform your precious memories into stunning, one-of-a-kind rhinestone artwork. Handcrafted by Kenyan artisans.",
    creator: "@bedazzled_ke",
    images: ['/favicon.ico'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="min-h-screen bg-background font-sans antialiased">
        <AuthProvider>
          <CartProvider>
            <div className="relative flex min-h-screen flex-col">
              <Header />
              <main className="flex-1">{children}</main>
              <Footer />
            </div>
          </CartProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
