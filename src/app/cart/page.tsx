'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ShoppingBagIcon,
  ArrowRightIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CartItem } from '@/components/cart/cart-item';
import { formatPrice } from '@/lib/utils';
import { Cart, CartItem as CartItemType } from '@/types';



export default function CartPage() {
  const [cart, setCart] = useState<Cart | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch cart on component mount
  useEffect(() => {
    fetchCart();
  }, []);

  const fetchCart = async () => {
    try {
      setIsLoading(true);
      // TODO: Replace with actual cart API endpoint
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/cart`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });

      if (response.ok) {
        const cartData = await response.json();
        setCart(cartData);
      } else {
        // Initialize empty cart if none exists
        setCart({
          id: '',
          userId: '',
          items: [],
          total: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
    } catch (error) {
      console.error('Error fetching cart:', error);
      // Initialize empty cart on error
      setCart({
        id: '',
        userId: '',
        items: [],
        total: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateQuantity = (itemId: string, quantity: number) => {
    if (!cart) return;

    setCart(prevCart => {
      if (!prevCart) return null;
      const updatedItems = prevCart.items.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      );
      return {
        ...prevCart,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    });
  };

  const handleRemoveItem = (itemId: string) => {
    if (!cart) return;

    setCart(prevCart => {
      if (!prevCart) return null;
      const updatedItems = prevCart.items.filter(item => item.id !== itemId);
      return {
        ...prevCart,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    });
  };

  const calculateTotal = (items: CartItemType[]) => {
    return items.reduce((total, item) => total + (item.product.price * item.quantity), 0);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading your cart...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (!cart) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <p className="text-muted-foreground">Unable to load cart. Please try again.</p>
        </div>
      </div>
    );
  }

  const total = calculateTotal(cart.items);

  if (cart.items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-accent/20 to-highlight/20 rounded-full flex items-center justify-center">
              <ShoppingBagIcon className="h-12 w-12 text-muted-foreground" />
            </div>
            <h1 className="text-2xl font-bold text-foreground mb-4">
              Your Cart is Empty
            </h1>
            <p className="text-muted-foreground mb-8">
              Looks like you haven't added any bedazzled portraits to your cart yet.
            </p>
            <Link href="/products">
              <Button size="lg">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Continue Shopping
              </Button>
            </Link>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="mb-8"
      >
        <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
          Shopping Cart
        </h1>
        <p className="text-muted-foreground">
          Review your selected items and proceed to checkout
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Cart Items */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Cart Items ({cart.items.length})</span>
                <Link href="/products">
                  <Button variant="outline" size="sm">
                    <ArrowLeftIcon className="h-4 w-4 mr-2" />
                    Continue Shopping
                  </Button>
                </Link>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <AnimatePresence>
                {cart.items.map((item) => (
                  <CartItem
                    key={item.id}
                    item={item}
                    onUpdateQuantity={handleUpdateQuantity}
                    onRemove={handleRemoveItem}
                  />
                ))}
              </AnimatePresence>
            </CardContent>
          </Card>
        </div>

        {/* Order Summary */}
        <div className="lg:col-span-1">
          <Card className="sticky top-4">
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Summary Details */}
              <div className="space-y-2">
                <div className="border-t border-border pt-2">
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total</span>
                    <span>{formatPrice(total)}</span>
                  </div>
                </div>
              </div>

              {/* Checkout Button */}
              <Button 
                size="lg" 
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? (
                  'Processing...'
                ) : (
                  <>
                    Proceed to Checkout
                    <ArrowRightIcon className="h-4 w-4 ml-2" />
                  </>
                )}
              </Button>

              {/* Security Notice */}
              <div className="text-center">
                <p className="text-xs text-muted-foreground">
                  🔒 Secure checkout with SSL encryption
                </p>
              </div>

              {/* Payment Methods */}
              <div className="text-center">
                <p className="text-xs text-muted-foreground mb-2">We accept:</p>
                <div className="flex justify-center gap-2">
                  <div className="w-8 h-5 bg-muted rounded flex items-center justify-center text-xs">💳</div>
                  <div className="w-8 h-5 bg-muted rounded flex items-center justify-center text-xs">🏦</div>
                  <div className="w-8 h-5 bg-muted rounded flex items-center justify-center text-xs">📱</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
