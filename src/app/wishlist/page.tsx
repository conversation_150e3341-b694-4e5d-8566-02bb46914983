'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  HeartIcon,
  ShoppingCartIcon,
  TrashIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ProductCard } from '@/components/products/product-card';
import { formatPrice } from '@/lib/utils';
import { Product } from '@/types';



export default function WishlistPage() {
  const [wishlistProducts, setWishlistProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch wishlist on component mount
  useEffect(() => {
    fetchWishlist();
  }, []);

  const fetchWishlist = async () => {
    try {
      setIsLoading(true);
      // TODO: Replace with actual wishlist API endpoint
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/wishlist`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setWishlistProducts(data.products || []);
      } else {
        console.error('Failed to fetch wishlist');
        setWishlistProducts([]);
      }
    } catch (error) {
      console.error('Error fetching wishlist:', error);
      setWishlistProducts([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveFromWishlist = (productId: string) => {
    setWishlistProducts(prev => prev.filter(product => product.id !== productId));
  };

  const handleAddToCart = (productId: string, variantId: string) => {
    // Implement add to cart functionality
    console.log('Add to cart:', productId, variantId);
  };

  const handleMoveAllToCart = () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      wishlistProducts.forEach(product => {
        handleAddToCart(product.id, product.variants[0].id);
      });
      setWishlistProducts([]);
      setIsLoading(false);
    }, 1000);
  };

  const totalValue = wishlistProducts.reduce((sum, product) => sum + product.basePrice, 0);

  if (wishlistProducts.length === 0) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-accent/20 to-highlight/20 rounded-full flex items-center justify-center">
              <HeartIcon className="h-12 w-12 text-muted-foreground" />
            </div>
            <h1 className="text-2xl font-bold text-foreground mb-4">
              Your Wishlist is Empty
            </h1>
            <p className="text-muted-foreground mb-8">
              Save your favorite bedazzled portraits to your wishlist and never lose track of them.
            </p>
            <Link href="/products">
              <Button size="lg">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Discover Products
              </Button>
            </Link>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="mb-8"
      >
        <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
          My Wishlist
        </h1>
        <p className="text-muted-foreground">
          Your saved bedazzled portraits ({wishlistProducts.length} items)
        </p>
      </motion.div>

      {/* Wishlist Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
        className="mb-8"
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Wishlist Summary</span>
              <Badge variant="secondary">
                {wishlistProducts.length} items
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Total Value</p>
                <p className="text-2xl font-bold text-foreground">
                  {formatPrice(totalValue)}
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setWishlistProducts([])}
                  disabled={isLoading}
                >
                  <TrashIcon className="h-4 w-4 mr-2" />
                  Clear All
                </Button>
                <Button
                  onClick={handleMoveAllToCart}
                  disabled={isLoading}
                >
                  <ShoppingCartIcon className="h-4 w-4 mr-2" />
                  {isLoading ? 'Adding...' : 'Add All to Cart'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Wishlist Products */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <AnimatePresence>
            {wishlistProducts.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <ProductCard
                  product={product}
                  isWishlisted={true}
                  onAddToWishlist={handleRemoveFromWishlist}
                  onAddToCart={handleAddToCart}
                />
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      </motion.div>

      {/* Continue Shopping */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
        className="mt-12 text-center"
      >
        <Card className="max-w-md mx-auto">
          <CardContent className="pt-6">
            <h3 className="font-semibold text-foreground mb-2">
              Looking for more?
            </h3>
            <p className="text-sm text-muted-foreground mb-4">
              Discover more beautiful bedazzled portraits in our collection.
            </p>
            <Link href="/products">
              <Button variant="outline" className="w-full">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Continue Shopping
              </Button>
            </Link>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
