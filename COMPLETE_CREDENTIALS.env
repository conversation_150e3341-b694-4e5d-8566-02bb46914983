# =============================================================================
# DAZZLED PLATFORM - COMPLETE CREDENTIALS FILE
# =============================================================================
# This file contains ALL environment variables for the entire platform
# 
# SECURITY WARNING: This file contains sensitive credentials
# - Never commit this file to version control
# - Keep this file secure and restrict access
# - Use different credentials for production
#
# Usage:
# - Copy relevant sections to .env.local (frontend) and backend/.env (backend)
# - Or use this as a reference for setting up environment variables
# =============================================================================

# =============================================================================
# FRONTEND ENVIRONMENT VARIABLES (.env.local)
# =============================================================================

# API Configuration (Required)
NEXT_PUBLIC_API_URL=http://localhost:3002/api
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3001

# Google OAuth (Required)
NEXT_PUBLIC_GOOGLE_CLIENT_ID=831471427369-hcdkbtnervmo3pcr0gka8elaqd8nqg2k.apps.googleusercontent.com

# Application Configuration
NODE_ENV=development
NEXT_PUBLIC_APP_NAME=Dazzled
NEXT_PUBLIC_APP_VERSION=1.0.0

# File Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# Feature Flags
NEXT_PUBLIC_ENABLE_CUSTOM_ORDERS=true
NEXT_PUBLIC_ENABLE_WISHLIST=false
NEXT_PUBLIC_ENABLE_REVIEWS=true
NEXT_PUBLIC_ENABLE_ADMIN_DASHBOARD=true

# SEO and Social Media
NEXT_PUBLIC_SITE_NAME=Dazzled - Rhinestone Bedazzled Portraits
NEXT_PUBLIC_SITE_DESCRIPTION=Transform your memories into stunning rhinestone bedazzled portraits
NEXT_PUBLIC_SITE_URL=http://localhost:3001

# Development Configuration
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_SHOW_DEV_TOOLS=true

# Currency and Localization
NEXT_PUBLIC_DEFAULT_CURRENCY=KES
NEXT_PUBLIC_DEFAULT_LOCALE=en-KE
NEXT_PUBLIC_TIMEZONE=Africa/Nairobi

# Contact Information
NEXT_PUBLIC_CONTACT_EMAIL=<EMAIL>
NEXT_PUBLIC_CONTACT_PHONE=+254700000000
NEXT_PUBLIC_BUSINESS_ADDRESS=Nairobi, Kenya

# =============================================================================
# BACKEND ENVIRONMENT VARIABLES (backend/.env)
# =============================================================================

# Database Configuration (Required)
MONGODB_URI=mongodb+srv://connect:<EMAIL>/?retryWrites=true&w=majority&appName=dazzled
DB_NAME=dazzled

# JWT Configuration (Required)
JWT_SECRET=your-super-secret-jwt-key-change-in-production-make-it-very-long-and-random
JWT_EXPIRES_IN=100d
JWT_REFRESH_EXPIRES_IN=30d

# Google OAuth Configuration (Required)
GOOGLE_CLIENT_ID=831471427369-hcdkbtnervmo3pcr0gka8elaqd8nqg2k.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Application Configuration (Required)
PORT=3002
NODE_ENV=development
APP_NAME=Dazzled API
APP_VERSION=1.0.0
FRONTEND_URL=http://localhost:3001

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DEST=./uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp
MAX_FILES_PER_UPLOAD=10

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=mpmp vqgy timx iqks
SMTP_FROM_NAME=Dazzled
SMTP_FROM=<EMAIL>
FROM_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
BCRYPT_ROUNDS=12
SESSION_SECRET=another-super-secret-key-for-sessions-change-this-too

# Logging and Monitoring
LOG_LEVEL=info
DEBUG=true
ENABLE_DOCS=true

# Cache Configuration
CACHE_TTL=3600

# Business Configuration
DEFAULT_SHIPPING_COST=500
FREE_SHIPPING_THRESHOLD=5000
DEFAULT_TAX_RATE=0.16
DEFAULT_CURRENCY=KES
ORDER_NUMBER_PREFIX=DZL

# Development Configuration
SEED_DATA=true
TEST_MONGODB_URI=mongodb://localhost:27017/dazzled_test

# =============================================================================
# OPTIONAL SERVICES (Add when needed)
# =============================================================================

# Payment Processing - Stripe
# STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
# STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
# STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
# NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here

# Payment Processing - M-Pesa (Kenya)
# MPESA_CONSUMER_KEY=your_mpesa_consumer_key
# MPESA_CONSUMER_SECRET=your_mpesa_consumer_secret
# MPESA_SHORTCODE=your_business_shortcode
# MPESA_PASSKEY=your_mpesa_passkey
# MPESA_CALLBACK_URL=https://yourdomain.com/api/payments/mpesa/callback

# Cloud Storage - AWS S3
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_REGION=us-east-1
# AWS_S3_BUCKET=dazzled-uploads

# Cloud Storage - Cloudinary
# CLOUDINARY_CLOUD_NAME=your_cloud_name
# CLOUDINARY_API_KEY=your_api_key
# CLOUDINARY_API_SECRET=your_api_secret

# Email Service - SendGrid (Alternative to SMTP)
# SENDGRID_API_KEY=SG.your_sendgrid_api_key

# Analytics and Monitoring
# NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
# NEXT_PUBLIC_HOTJAR_ID=your_hotjar_id
# SENTRY_DSN=https://<EMAIL>/project_id

# Cache - Redis
# REDIS_URL=redis://localhost:6379
# REDIS_PASSWORD=your_redis_password

# Social Media Links
# NEXT_PUBLIC_FACEBOOK_URL=https://facebook.com/dazzled
# NEXT_PUBLIC_INSTAGRAM_URL=https://instagram.com/dazzled
# NEXT_PUBLIC_TWITTER_URL=https://twitter.com/dazzled

# =============================================================================
# PRODUCTION OVERRIDES (Use different values for production)
# =============================================================================

# Production Database
# MONGODB_URI=***************************************************************************

# Production JWT (Generate new secret!)
# JWT_SECRET=production_jwt_secret_64_characters_minimum_very_secure_random_string

# Production Google OAuth
# GOOGLE_CLIENT_ID=your_production_google_client_id.apps.googleusercontent.com
# GOOGLE_CLIENT_SECRET=GOCSPX-your_production_google_client_secret

# Production URLs
# NEXT_PUBLIC_API_URL=https://api.dazzled.com/api
# NEXT_PUBLIC_FRONTEND_URL=https://dazzled.com
# FRONTEND_URL=https://dazzled.com
# NEXT_PUBLIC_SITE_URL=https://dazzled.com

# Production Email
# SMTP_USER=<EMAIL>
# SMTP_PASS=production_app_password

# Production Environment
# NODE_ENV=production
# DEBUG=false
# SEED_DATA=false

# =============================================================================
# SETUP COMMANDS
# =============================================================================
# 
# 1. Copy frontend variables to .env.local:
#    cp COMPLETE_CREDENTIALS.env .env.local
#    # Then edit .env.local to keep only NEXT_PUBLIC_* variables
#
# 2. Copy backend variables to backend/.env:
#    cp COMPLETE_CREDENTIALS.env backend/.env
#    # Then edit backend/.env to remove NEXT_PUBLIC_* variables
#
# 3. Install dependencies:
#    pnpm install:concurrent
#
# 4. Validate setup:
#    pnpm validate-env
#
# 5. Start development servers:
#    pnpm dev
#
# =============================================================================
