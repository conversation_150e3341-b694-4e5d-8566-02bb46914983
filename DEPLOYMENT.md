# Deployment Guide - Bedazzled E-commerce Platform

This guide covers deploying the Bedazzled platform to production environments.

## 🚀 Quick Deployment Options

### Option 1: Vercel (Frontend) + Railway (Backend)
**Recommended for MVP deployment**

### Option 2: Vercel (Frontend) + <PERSON><PERSON> (Backend)
**Alternative cloud deployment**

### Option 3: VPS Deployment
**For full control and custom configurations**

## 📋 Pre-deployment Checklist

- [ ] Environment variables configured
- [ ] Database setup (MongoDB Atlas recommended)
- [ ] Domain names configured
- [ ] SSL certificates ready
- [ ] Payment gateway credentials (M-Pesa)
- [ ] Email service configured
- [ ] Image storage configured (Cloudinary/AWS S3)

## 🌐 Frontend Deployment (Vercel)

### 1. Prepare for Deployment

```bash
# Build and test locally
pnpm build
pnpm start

# Check for build errors
pnpm lint
```

### 2. Deploy to Vercel

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

### 3. Environment Variables (Vercel)

In your Vercel dashboard, add these environment variables:

```env
NEXT_PUBLIC_API_URL=https://your-backend-url.railway.app
NEXT_PUBLIC_ENABLE_WISHLIST=false
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

### 4. Custom Domain (Optional)

1. Go to Vercel dashboard
2. Select your project
3. Go to Settings > Domains
4. Add your custom domain
5. Configure DNS records

## 🖥️ Backend Deployment (Railway)

### 1. Prepare Backend

```bash
cd backend

# Install dependencies
pnpm install

# Build for production
pnpm build

# Test production build
pnpm start:prod
```

### 2. Deploy to Railway

1. **Create Railway Account**: Go to [railway.app](https://railway.app)
2. **Connect GitHub**: Link your repository
3. **Create New Project**: Select your repository
4. **Configure Build**: Railway auto-detects NestJS

### 3. Environment Variables (Railway)

In Railway dashboard, add these variables:

```env
NODE_ENV=production
PORT=3002
MONGODB_URI=mongodb+srv://username:<EMAIL>/bedazzled
JWT_SECRET=your-super-secure-jwt-secret-key
JWT_EXPIRES_IN=7d
CORS_ORIGIN=https://your-frontend-domain.vercel.app
```

### 4. Database Setup (MongoDB Atlas)

1. **Create MongoDB Atlas Account**: [mongodb.com/cloud/atlas](https://mongodb.com/cloud/atlas)
2. **Create Cluster**: Choose free tier for MVP
3. **Create Database User**: With read/write permissions
4. **Configure Network Access**: Add 0.0.0.0/0 for Railway
5. **Get Connection String**: Use in MONGODB_URI

## 🔧 Alternative: Heroku Deployment

### Backend on Heroku

```bash
# Install Heroku CLI
npm install -g heroku

# Login to Heroku
heroku login

# Create Heroku app
heroku create your-app-name

# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set MONGODB_URI=your-mongodb-uri
heroku config:set JWT_SECRET=your-jwt-secret

# Deploy
git push heroku main
```

### Heroku Procfile

Create `backend/Procfile`:
```
web: npm run start:prod
```

## 🖥️ VPS Deployment (Ubuntu)

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install pnpm
npm install -g pnpm

# Install PM2 for process management
npm install -g pm2

# Install Nginx
sudo apt install nginx -y
```

### 2. Deploy Application

```bash
# Clone repository
git clone https://github.com/your-username/bedazzled.git
cd bedazzled

# Install dependencies
pnpm setup

# Build applications
pnpm build:full

# Start backend with PM2
cd backend
pm2 start dist/main.js --name "bedazzled-backend"

# Start frontend with PM2
cd ..
pm2 start npm --name "bedazzled-frontend" -- start

# Save PM2 configuration
pm2 save
pm2 startup
```

### 3. Nginx Configuration

Create `/etc/nginx/sites-available/bedazzled`:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # Frontend
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Backend API
    location /api {
        proxy_pass http://localhost:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/bedazzled /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 4. SSL Certificate (Let's Encrypt)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Get SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔒 Security Considerations

### Environment Variables
- Use strong, unique JWT secrets
- Rotate secrets regularly
- Never commit secrets to version control

### Database Security
- Use MongoDB Atlas with IP whitelisting
- Enable authentication
- Use connection string with credentials

### API Security
- Enable CORS with specific origins
- Implement rate limiting
- Use HTTPS in production
- Validate all inputs

### Frontend Security
- Sanitize user inputs
- Implement CSP headers
- Use secure cookies
- Enable HTTPS redirects

## 📊 Monitoring & Maintenance

### Health Checks

Create health check endpoints:

**Backend**: `GET /health`
```typescript
@Get('health')
healthCheck() {
  return { status: 'ok', timestamp: new Date().toISOString() };
}
```

**Frontend**: `GET /api/health`
```typescript
export async function GET() {
  return Response.json({ status: 'ok', timestamp: new Date().toISOString() });
}
```

### Monitoring Tools
- **Uptime**: UptimeRobot, Pingdom
- **Performance**: Vercel Analytics, Google PageSpeed
- **Errors**: Sentry, LogRocket
- **Database**: MongoDB Atlas monitoring

### Backup Strategy
- **Database**: MongoDB Atlas automatic backups
- **Code**: Git repository with tags for releases
- **Assets**: Cloud storage with versioning

## 🚀 CI/CD Pipeline (GitHub Actions)

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm install -g pnpm
      - run: pnpm install
      - run: pnpm build
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'

  deploy-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm install -g pnpm
      - run: cd backend && pnpm install
      - run: cd backend && pnpm build
      # Deploy to Railway/Heroku
```

## 📈 Performance Optimization

### Frontend Optimization
- Enable Next.js Image Optimization
- Implement lazy loading
- Use CDN for static assets
- Enable compression (gzip/brotli)

### Backend Optimization
- Implement caching (Redis)
- Database indexing
- Connection pooling
- API response compression

### Database Optimization
- Create proper indexes
- Implement pagination
- Use aggregation pipelines
- Monitor slow queries

## 🎯 Post-Deployment Tasks

1. **Test all functionality**
   - User registration/login
   - Product browsing
   - Cart operations
   - Order placement
   - Admin functions

2. **Configure monitoring**
   - Set up uptime monitoring
   - Configure error tracking
   - Set up performance monitoring

3. **SEO Setup**
   - Submit sitemap to Google
   - Configure Google Analytics
   - Set up Google Search Console

4. **Marketing Setup**
   - Configure social media links
   - Set up email marketing
   - Configure payment gateways

## 🆘 Troubleshooting

### Common Issues

**Build Failures**
- Check Node.js version compatibility
- Verify environment variables
- Check for TypeScript errors

**Database Connection Issues**
- Verify MongoDB URI
- Check network access rules
- Confirm database user permissions

**CORS Errors**
- Update CORS_ORIGIN in backend
- Check API URL in frontend
- Verify protocol (http/https)

**Performance Issues**
- Check database queries
- Monitor memory usage
- Verify CDN configuration

For additional support, check the main README.md or create an issue in the repository.
