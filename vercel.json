{"version": 2, "name": "dazzled", "builds": [{"src": "package.json", "use": "@vercel/next"}], "buildCommand": "pnpm install && pnpm build", "installCommand": "pnpm install", "devCommand": "pnpm dev:frontend", "framework": "nextjs", "outputDirectory": ".next", "ignoreCommand": "git diff --quiet HEAD^ HEAD ./", "env": {"NODE_ENV": "production"}, "functions": {"app/api/**/*.ts": {"runtime": "nodejs18.x"}}, "rewrites": [{"source": "/api/:path*", "destination": "/api/:path*"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}