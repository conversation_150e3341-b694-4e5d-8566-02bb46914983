# Bedazzled - Rhinestone Bedazzled Portraits E-commerce Platform

A comprehensive e-commerce platform for selling rhinestone bedazzled portraits, built with modern web technologies.

## 🌟 Features

### Frontend (Next.js 15 + TypeScript)
- **Modern UI/UX**: Responsive design with Tailwind CSS and Framer Motion animations
- **Product Catalog**: Simplified category-based filtering for MVP
- **Shopping Cart**: Streamlined cart management with real-time updates
- **Custom Orders**: Simplified form for custom portrait requests
- **Admin Dashboard**: Comprehensive admin panel for product and order management
- **Authentication**: JWT-based authentication with role-based access
- **Responsive Design**: Mobile-first approach with premium animations

### Backend (NestJS + MongoDB)
- **RESTful API**: Complete API with authentication and authorization
- **Simplified Architecture**: Streamlined schemas for products, orders, and custom orders
- **Dual Order System**: Handles both product orders and custom portrait requests
- **Role-based Access**: Admin and user role management
- **Order Management**: Complete order lifecycle with status tracking

### Key Highlights
- **Scalable Architecture**: Modular design for easy maintenance and expansion
- **Type Safety**: Full TypeScript implementation across frontend and backend
- **Testing Ready**: Jest configuration with sample tests
- **Production Ready**: Optimized build process and deployment configuration

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- pnpm (recommended) or npm
- MongoDB (for backend)

### Frontend Setup
```bash
# Install dependencies
pnpm install

# Start development server (runs on port 3001)
pnpm dev:frontend

# Run tests
pnpm test

# Build for production
pnpm build
```

### Backend Setup
```bash
# Navigate to backend directory
cd backend

# Install dependencies
pnpm install

# Set up environment variables
cp .env.example .env
# Edit .env with your MongoDB URI and other settings

# Start development server (runs on port 3002)
pnpm start:dev

# Build for production
pnpm build
```

### Quick Start (Both Frontend & Backend)
```bash
# Install all dependencies
pnpm setup

# Start both servers concurrently
# Frontend: http://localhost:3001
# Backend: http://localhost:3002/api
pnpm dev
```

## 📁 Project Structure

```
bedazzled/
├── src/                          # Frontend source
│   ├── app/                      # Next.js app router pages
│   │   ├── admin/               # Admin dashboard
│   │   ├── auth/                # Authentication pages
│   │   ├── products/            # Product pages
│   │   ├── cart/                # Shopping cart
│   │   ├── checkout/            # Checkout process
│   │   └── custom-orders/       # Custom order requests
│   ├── components/              # Reusable components
│   │   ├── ui/                  # Base UI components
│   │   ├── layout/              # Layout components
│   │   ├── products/            # Product-specific components
│   │   └── admin/               # Admin components
│   ├── contexts/                # React contexts
│   ├── lib/                     # Utility functions
│   └── types/                   # TypeScript types
├── backend/                     # Backend source
│   ├── src/
│   │   ├── auth/               # Authentication module
│   │   ├── products/           # Products module
│   │   ├── orders/             # Orders module
│   │   ├── custom-orders/      # Custom orders module
│   │   ├── admin/              # Admin module
│   │   └── schemas/            # Database schemas
└── public/                     # Static assets
```

## 🎨 Design System

### Color Palette
- **Primary**: White-based minimalistic design
- **Accent**: Rhinestone/bedazzled colors with gold touches
- **Currency**: KES (Kenyan Shilling)
- **Responsive**: Mobile-first approach

### Components
- **Rounded Headers**: Consistent rounded design with margins
- **Framer Motion**: Premium animations throughout
- **Tailwind CSS**: Utility-first styling
- **Shadcn/ui**: Consistent component library

## 🔐 Authentication & Authorization

### User Roles
- **Guest**: Browse products, limited access
- **Authenticated User**: Full e-commerce functionality
- **Admin**: Product and order management

### Access Control
- Role-based navigation
- Protected routes
- JWT token authentication
- Secure admin areas

## 📱 API Endpoints

### Products
- `GET /products` - List products with filtering
- `GET /products/:id` - Get product details
- `POST /products` - Create product (admin)
- `PATCH /products/:id` - Update product (admin)
- `DELETE /products/:id` - Delete product (admin)

### Orders
- `GET /orders` - List orders (admin)
- `GET /orders/my-orders` - User's orders
- `POST /orders` - Create order
- `PATCH /orders/:id/status` - Update order status (admin)

### Custom Orders
- `GET /custom-orders` - List custom orders (admin)
- `GET /custom-orders/my-orders` - User's custom orders
- `POST /custom-orders` - Create custom order
- `PATCH /custom-orders/:id/status` - Update status (admin)

### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `GET /auth/profile` - Get user profile
- `PATCH /auth/profile` - Update profile

## 🛒 E-commerce Features

### Simplified Architecture
- **No Variants**: Single price/stock per product
- **Basic Filtering**: Category-based filtering only
- **Simple Checkout**: M-Pesa and Cash on Delivery
- **Essential Fields**: Streamlined product and order data

### Cart Management
- Real-time cart updates
- Persistent cart (logged-in users)
- Quantity management
- Total calculations

## 🌍 Kenyan Market Features

- **Currency**: KES (Kenyan Shilling) formatting
- **Payment Methods**: M-Pesa integration ready
- **Local Artisans**: Supporting Kenyan craftspeople
- **Business Hours**: Local timezone considerations

## 🚀 Deployment

### Frontend (Vercel)
```bash
# Build for production
pnpm build

# Deploy to Vercel
vercel --prod
```

### Backend (Railway/Heroku)
```bash
cd backend
# Build for production
pnpm build

# Deploy to your preferred platform
```

## 🎯 MVP Focus

This implementation focuses on core e-commerce functionality:
- ✅ Simplified product management (no complex variants)
- ✅ Basic category filtering
- ✅ Streamlined cart and checkout
- ✅ Dual order system (products + custom orders)
- ✅ Admin dashboard with essential features
- ✅ Mobile-responsive design
- ✅ Production-ready architecture

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support, email <EMAIL> or create an issue in the repository.
