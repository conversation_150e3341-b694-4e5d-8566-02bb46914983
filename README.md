# Bedazzled - Rhinestone Bedazzled Portraits E-commerce Platform

A comprehensive e-commerce platform for selling rhinestone bedazzled portraits, built with modern web technologies.

## 🌟 Features

### Frontend (Next.js 15 + TypeScript)
- **Modern UI/UX**: Responsive design with Tailwind CSS and Framer Motion animations
- **Product Catalog**: Advanced filtering, search, and sorting capabilities
- **Shopping Cart**: Full cart management with quantity updates and price calculations
- **Wishlist**: Save favorite products for later
- **Custom Orders**: Complete form system for custom portrait requests
- **Admin Dashboard**: Comprehensive admin panel for order and product management
- **Authentication Ready**: Google OAuth integration structure in place

### Backend (NestJS + MongoDB)
- **RESTful API**: Complete API with authentication and authorization
- **Database Models**: Comprehensive schemas for users, products, orders, reviews, and more
- **File Upload**: Image upload system for products and custom orders
- **Role-based Access**: Admin and buyer role management
- **Order Management**: Complete order lifecycle management

### Key Highlights
- **Scalable Architecture**: Modular design for easy maintenance and expansion
- **Type Safety**: Full TypeScript implementation across frontend and backend
- **Testing Ready**: Jest configuration with sample tests
- **Production Ready**: Optimized build process and deployment configuration

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- pnpm (recommended) or npm
- MongoDB (for backend)

### Frontend Setup
```bash
# Install dependencies
pnpm install

# Start development server (runs on port 3001)
pnpm dev:frontend

# Run tests
pnpm test

# Build for production
pnpm build
```

### Backend Setup
```bash
# Navigate to backend directory
cd backend

# Install dependencies
pnpm install

# Set up environment variables
cp .env.example .env
# Edit .env with your MongoDB URI and other settings

# Start development server (runs on port 3002)
pnpm start:dev

# Build for production
pnpm build
```

### Quick Start (Both Frontend & Backend)
```bash
# Install all dependencies
pnpm setup

# Start both servers concurrently
# Frontend: http://localhost:3001
# Backend: http://localhost:3002/api
pnpm dev
```
