#!/usr/bin/env node

/**
 * Environment Validation Script for Bedazzled Platform
 * 
 * This script validates that all required environment variables are set
 * and provides helpful feedback for missing or invalid configurations.
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bold: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log(`\n${colors.bold}${colors.cyan}=== ${message} ===${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Required environment variables for frontend
const frontendRequiredVars = [
  'NEXT_PUBLIC_API_URL',
  'NEXT_PUBLIC_FRONTEND_URL'
];

// Optional but recommended frontend variables
const frontendOptionalVars = [
  'NEXT_PUBLIC_GOOGLE_CLIENT_ID',
  'NEXT_PUBLIC_MAX_FILE_SIZE',
  'NEXT_PUBLIC_APP_NAME'
];

// Required environment variables for backend
const backendRequiredVars = [
  'MONGODB_URI',
  'JWT_SECRET',
  'PORT',
  'FRONTEND_URL'
];

// Optional but recommended backend variables
const backendOptionalVars = [
  'GOOGLE_CLIENT_ID',
  'GOOGLE_CLIENT_SECRET',
  'SMTP_HOST',
  'SMTP_USER',
  'SMTP_PASS',
  'STRIPE_SECRET_KEY'
];

function loadEnvFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return null;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    const env = {};
    
    content.split('\n').forEach(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          env[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    
    return env;
  } catch (error) {
    logError(`Error reading ${filePath}: ${error.message}`);
    return null;
  }
}

function validateUrl(url, name) {
  try {
    new URL(url);
    logSuccess(`${name}: ${url}`);
    return true;
  } catch {
    logError(`${name}: Invalid URL format - ${url}`);
    return false;
  }
}

function validateMongoUri(uri) {
  if (uri.startsWith('mongodb://') || uri.startsWith('mongodb+srv://')) {
    logSuccess(`MongoDB URI: ${uri.substring(0, 20)}...`);
    return true;
  } else {
    logError(`MongoDB URI: Invalid format - should start with mongodb:// or mongodb+srv://`);
    return false;
  }
}

function validateJwtSecret(secret) {
  if (secret.length < 32) {
    logWarning(`JWT Secret: Too short (${secret.length} chars). Recommended: 64+ characters`);
    return false;
  } else if (secret.includes('change-this') || secret.includes('development-only')) {
    logWarning(`JWT Secret: Using default/development secret. Generate a new one for production!`);
    return false;
  } else {
    logSuccess(`JWT Secret: Good length (${secret.length} characters)`);
    return true;
  }
}

function validateEmailConfig(env) {
  const hasSmtp = env.SMTP_HOST && env.SMTP_USER && env.SMTP_PASS;
  const hasSendGrid = env.SENDGRID_API_KEY;
  
  if (hasSmtp) {
    logSuccess(`Email: SMTP configured (${env.SMTP_HOST})`);
    return true;
  } else if (hasSendGrid) {
    logSuccess(`Email: SendGrid configured`);
    return true;
  } else {
    logWarning(`Email: No email service configured (SMTP or SendGrid)`);
    return false;
  }
}

function validateFrontendEnv() {
  logHeader('Frontend Environment Validation');
  
  const envPath = path.join(process.cwd(), '.env.local');
  const env = loadEnvFile(envPath);
  
  if (!env) {
    logError(`Frontend .env.local file not found at ${envPath}`);
    logInfo('Copy .env.local.example to .env.local and configure it');
    return false;
  }
  
  let isValid = true;
  
  // Check required variables
  log('\nRequired Variables:');
  frontendRequiredVars.forEach(varName => {
    if (env[varName]) {
      if (varName.includes('URL')) {
        isValid = validateUrl(env[varName], varName) && isValid;
      } else {
        logSuccess(`${varName}: ${env[varName]}`);
      }
    } else {
      logError(`${varName}: Missing`);
      isValid = false;
    }
  });
  
  // Check optional variables
  log('\nOptional Variables:');
  frontendOptionalVars.forEach(varName => {
    if (env[varName]) {
      logSuccess(`${varName}: Configured`);
    } else {
      logWarning(`${varName}: Not configured`);
    }
  });
  
  return isValid;
}

function validateBackendEnv() {
  logHeader('Backend Environment Validation');
  
  const envPath = path.join(process.cwd(), 'backend', '.env');
  const env = loadEnvFile(envPath);
  
  if (!env) {
    logError(`Backend .env file not found at ${envPath}`);
    logInfo('Copy backend/.env.example to backend/.env and configure it');
    return false;
  }
  
  let isValid = true;
  
  // Check required variables
  log('\nRequired Variables:');
  backendRequiredVars.forEach(varName => {
    if (env[varName]) {
      if (varName === 'MONGODB_URI') {
        isValid = validateMongoUri(env[varName]) && isValid;
      } else if (varName === 'JWT_SECRET') {
        isValid = validateJwtSecret(env[varName]) && isValid;
      } else if (varName.includes('URL')) {
        isValid = validateUrl(env[varName], varName) && isValid;
      } else {
        logSuccess(`${varName}: ${env[varName]}`);
      }
    } else {
      logError(`${varName}: Missing`);
      isValid = false;
    }
  });
  
  // Check optional variables
  log('\nOptional Variables:');
  backendOptionalVars.forEach(varName => {
    if (env[varName]) {
      logSuccess(`${varName}: Configured`);
    } else {
      logWarning(`${varName}: Not configured`);
    }
  });
  
  // Validate email configuration
  log('\nEmail Configuration:');
  validateEmailConfig(env);
  
  return isValid;
}

function checkDependencies() {
  logHeader('Dependency Check');
  
  // Check if package.json exists
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  if (fs.existsSync(packageJsonPath)) {
    logSuccess('Frontend package.json found');
  } else {
    logError('Frontend package.json not found');
    return false;
  }
  
  // Check if backend package.json exists
  const backendPackageJsonPath = path.join(process.cwd(), 'backend', 'package.json');
  if (fs.existsSync(backendPackageJsonPath)) {
    logSuccess('Backend package.json found');
  } else {
    logError('Backend package.json not found');
    return false;
  }
  
  // Check if node_modules exist
  const nodeModulesPath = path.join(process.cwd(), 'node_modules');
  if (fs.existsSync(nodeModulesPath)) {
    logSuccess('Frontend dependencies installed');
  } else {
    logWarning('Frontend dependencies not installed. Run: pnpm install');
  }
  
  const backendNodeModulesPath = path.join(process.cwd(), 'backend', 'node_modules');
  if (fs.existsSync(backendNodeModulesPath)) {
    logSuccess('Backend dependencies installed');
  } else {
    logWarning('Backend dependencies not installed. Run: cd backend && pnpm install');
  }
  
  return true;
}

function main() {
  log(`${colors.bold}${colors.magenta}🔍 Bedazzled Platform Environment Validator${colors.reset}\n`);
  
  const dependenciesOk = checkDependencies();
  const frontendOk = validateFrontendEnv();
  const backendOk = validateBackendEnv();
  
  logHeader('Summary');
  
  if (dependenciesOk && frontendOk && backendOk) {
    logSuccess('✨ All environment variables are properly configured!');
    logInfo('You can now start the development servers:');
    log('  Frontend (port 3001): pnpm dev');
    log('  Backend (port 3002):  cd backend && pnpm start:dev');
    log('  Or both together:     pnpm dev');
  } else {
    logError('❌ Some configuration issues were found.');
    logInfo('Please fix the issues above and run this script again.');
    logInfo('See SETUP_GUIDE.md for detailed setup instructions.');
  }
  
  log('\n📚 For help with setup, see: SETUP_GUIDE.md');
  log('🐛 For issues, check the console logs when starting the servers.\n');
}

// Run the validation
main();
