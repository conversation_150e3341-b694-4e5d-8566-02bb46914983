# Role-Based Header Implementation

This document describes the comprehensive role-based header system implemented for the Bedazzled e-commerce platform.

## 🎯 Overview

The header dynamically adapts based on the user's authentication status and role, providing contextual navigation and visual indicators for different user types.

## 🔐 User Roles & Navigation

### **Guest Users (Unauthenticated)**
**Navigation Items:**
- 🏠 Home
- 🛍️ Products

**Features:**
- Basic marketplace browsing
- Login/Register buttons
- No cart or user menu
- Redirected to login for protected actions

### **Authenticated Users**
**Navigation Items:**
- 🏠 Home
- 🛍️ Products  
- ✨ Custom Orders

**Features:**
- Shopping cart with item count badge
- User dropdown menu with profile access
- Order history access
- Full e-commerce functionality

### **Admin Users**
**Navigation Items:**
- 🏠 Home
- 🛍️ Products
- ✨ Custom Orders
- 📊 Dashboard (Admin)
- ⚙️ Manage Products (Admin)
- 📋 Orders (Admin)

**Features:**
- All user features plus admin tools
- Admin badge indicators
- Special admin styling (accent colors)
- Admin-specific dropdown menu items

## 🎨 Visual Design Features

### **Active State Indicators**
- **Current page highlighting** with accent colors
- **Admin routes** get special accent styling
- **Hover effects** with smooth transitions
- **Badge indicators** for admin functions

### **Responsive Design**
- **Desktop**: Horizontal navigation with icons and text
- **Mobile**: Collapsible hamburger menu
- **Tablet**: Adaptive layout with proper spacing

### **Role Indicators**
- **Admin Badge**: Shows "Admin" badge in user menu
- **Admin Navigation**: Accent-colored admin routes
- **Role-specific styling**: Different colors for admin vs user actions

## 🧩 Component Architecture

### **Header Component** (`src/components/layout/header.tsx`)
```tsx
interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  requiresAuth?: boolean;
  roles: string[];
}
```

**Key Features:**
- Dynamic navigation generation based on user role
- Active state detection using `usePathname()`
- Cart integration with real-time item count
- User menu with role-specific options

### **Breadcrumb System** (`src/components/layout/breadcrumb.tsx`)
```tsx
// Auto-generates breadcrumbs from URL path
<RoleBreadcrumb />

// Custom breadcrumbs for specific pages
<PageHeader title="Page Title" description="Description" />
```

### **Role-Based Layout** (`src/components/layout/role-based-layout.tsx`)
```tsx
<RoleBasedLayout 
  requiredRole="admin" 
  showBreadcrumbs={true}
>
  {children}
</RoleBasedLayout>
```

## 🔧 Implementation Details

### **Navigation Generation Logic**
```tsx
const getNavigationItems = (): NavigationItem[] => {
  const baseNavigation = [...]; // Home, Products
  const userNavigation = [...]; // Custom Orders
  const adminNavigation = [...]; // Admin tools
  
  // Combine based on user role
  let navigation = [...baseNavigation];
  if (isAuthenticated) {
    navigation = [...navigation, ...userNavigation];
    if (user?.role === 'admin') {
      navigation = [...navigation, ...adminNavigation];
    }
  }
  
  return navigation.filter(item => 
    item.roles.includes(user?.role || 'guest')
  );
};
```

### **Active State Detection**
```tsx
const isActive = pathname === item.href || 
  (item.href !== '/' && pathname.startsWith(item.href));
```

### **Admin Styling**
```tsx
const isAdminRoute = item.href.startsWith('/admin');

className={`${
  isActive
    ? isAdminRoute
      ? 'bg-accent text-accent-foreground shadow-sm'
      : 'bg-accent/10 text-accent border border-accent/20'
    : isAdminRoute 
      ? 'text-accent hover:bg-accent/10 border border-accent/20'
      : 'text-foreground hover:text-accent hover:bg-accent/5'
}`}
```

## 📱 User Experience Features

### **Cart Integration**
- Real-time cart item count badge
- Smooth animations for cart updates
- Cart icon only visible for authenticated users

### **User Menu**
```tsx
// Standard user menu
- Profile
- My Orders

// Admin additions
- Dashboard
- Manage Products  
- Manage Orders
```

### **Authentication Flow**
- **Unauthenticated**: Login/Register buttons
- **Authenticated**: User avatar/icon with dropdown
- **Protected routes**: Automatic redirect to login

### **Mobile Experience**
- Hamburger menu with full navigation
- Touch-friendly button sizes
- Smooth slide animations
- Role indicators maintained

## 🎯 Usage Examples

### **Basic Page with Breadcrumbs**
```tsx
export default function ProductsPage() {
  return (
    <RoleBasedLayout>
      <PageHeader 
        title="Products" 
        description="Browse our bedazzled portraits"
      />
      {/* Page content */}
    </RoleBasedLayout>
  );
}
```

### **Admin Page**
```tsx
export default function AdminProductsPage() {
  return (
    <RoleBasedLayout requiredRole="admin">
      <PageHeader 
        title="Product Management"
        description="Manage your products and inventory"
      >
        <Button onClick={handleAddProduct}>
          Add Product
        </Button>
      </PageHeader>
      {/* Admin content */}
    </RoleBasedLayout>
  );
}
```

### **Protected User Page**
```tsx
export default function OrdersPage() {
  return (
    <RoleBasedLayout requiredRole="authenticated">
      <PageHeader title="My Orders" />
      {/* User orders */}
    </RoleBasedLayout>
  );
}
```

## 🔄 State Management

### **Authentication Context**
- User role detection
- Authentication status
- Login/logout functionality

### **Cart Context**
- Real-time item count
- Cart state management
- Persistent cart for logged-in users

### **Navigation State**
- Active route detection
- Mobile menu toggle
- User menu toggle

## 🎨 Styling System

### **Color Palette**
- **Primary**: Standard navigation items
- **Accent**: Admin routes and active states
- **Muted**: Inactive and secondary elements
- **Foreground**: Text and icons

### **Animation System**
- **Framer Motion**: Smooth transitions
- **Hover effects**: Color and background changes
- **Mobile menu**: Slide animations
- **Badge updates**: Scale animations

## 🚀 Benefits

### **User Experience**
- **Intuitive navigation** based on user capabilities
- **Clear visual hierarchy** with role indicators
- **Consistent experience** across all devices
- **Contextual breadcrumbs** for better orientation

### **Developer Experience**
- **Type-safe navigation** with TypeScript
- **Reusable components** for consistent implementation
- **Easy role management** with declarative configuration
- **Maintainable code** with clear separation of concerns

### **Business Value**
- **Role-based access control** for security
- **Scalable architecture** for future roles
- **Professional appearance** with admin indicators
- **Mobile-optimized** for broader accessibility

This role-based header system provides a comprehensive, scalable solution for managing navigation across different user types while maintaining a consistent and professional user experience.
