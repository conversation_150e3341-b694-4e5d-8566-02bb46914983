# 🚀 Vercel Deployment Guide for Dazzled Platform

This guide covers deploying the Dazzled frontend to Vercel. Note that Vercel only hosts the frontend (Next.js) - the backend needs to be deployed separately.

## 📋 Pre-Deployment Checklist

### 1. Environment Variables Setup
Before deploying, you need to configure environment variables in Vercel:

#### Required Environment Variables
```bash
NEXT_PUBLIC_API_URL=https://your-backend-api.com/api
NEXT_PUBLIC_FRONTEND_URL=https://your-vercel-app.vercel.app
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-production-google-client-id.apps.googleusercontent.com
```

#### Optional Environment Variables
```bash
NEXT_PUBLIC_APP_NAME=Dazzled
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp
NEXT_PUBLIC_ENABLE_CUSTOM_ORDERS=true
NEXT_PUBLIC_ENABLE_WISHLIST=true
NEXT_PUBLIC_ENABLE_REVIEWS=true
NEXT_PUBLIC_ENABLE_ADMIN_DASHBOARD=true
NEXT_PUBLIC_SITE_NAME=Dazzled - Rhinestone Bedazzled Portraits
NEXT_PUBLIC_SITE_DESCRIPTION=Transform your memories into stunning rhinestone bedazzled portraits
NEXT_PUBLIC_SITE_URL=https://your-vercel-app.vercel.app
NEXT_PUBLIC_DEFAULT_CURRENCY=KES
NEXT_PUBLIC_DEFAULT_LOCALE=en-KE
NEXT_PUBLIC_TIMEZONE=Africa/Nairobi
NEXT_PUBLIC_CONTACT_EMAIL=<EMAIL>
NEXT_PUBLIC_CONTACT_PHONE=+254700000000
NEXT_PUBLIC_BUSINESS_ADDRESS=Nairobi, Kenya
NODE_ENV=production
```

### 2. Google OAuth Configuration
Update your Google OAuth settings for production:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Go to Credentials → OAuth 2.0 Client IDs
4. Add your Vercel domain to authorized redirect URIs:
   - `https://your-vercel-app.vercel.app/auth/callback`
   - `https://your-custom-domain.com/auth/callback` (if using custom domain)

### 3. Backend Deployment
The backend needs to be deployed separately. Recommended options:

#### Option A: Railway
1. Connect your GitHub repo to Railway
2. Deploy the `backend` folder
3. Configure environment variables
4. Get the deployed API URL

#### Option B: Render
1. Create a new Web Service on Render
2. Connect your GitHub repo
3. Set build command: `cd backend && pnpm install && pnpm build`
4. Set start command: `cd backend && pnpm start:prod`
5. Configure environment variables

#### Option C: Heroku
1. Create a new Heroku app
2. Set buildpack to Node.js
3. Configure environment variables
4. Deploy using Git subtree: `git subtree push --prefix backend heroku main`

## 🚀 Vercel Deployment Steps

### Method 1: Vercel CLI (Recommended)

1. **Install Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **Login to Vercel**
   ```bash
   vercel login
   ```

3. **Deploy from project root**
   ```bash
   vercel
   ```

4. **Follow the prompts:**
   - Set up and deploy? `Y`
   - Which scope? Choose your account
   - Link to existing project? `N` (for first deployment)
   - What's your project's name? `dazzled`
   - In which directory is your code located? `./`

5. **Configure environment variables**
   ```bash
   vercel env add NEXT_PUBLIC_API_URL
   vercel env add NEXT_PUBLIC_FRONTEND_URL
   vercel env add NEXT_PUBLIC_GOOGLE_CLIENT_ID
   # Add all other required variables
   ```

6. **Deploy to production**
   ```bash
   vercel --prod
   ```

### Method 2: GitHub Integration

1. **Push to GitHub** (already done)
   ```bash
   git push origin main
   ```

2. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Select the root directory (not backend)

3. **Configure Build Settings**
   - Framework Preset: `Next.js`
   - Build Command: `pnpm build` (or leave default)
   - Output Directory: `.next` (or leave default)
   - Install Command: `pnpm install`

4. **Add Environment Variables**
   - Go to Project Settings → Environment Variables
   - Add all the required variables listed above

5. **Deploy**
   - Click "Deploy"
   - Wait for deployment to complete

## 🔧 Configuration Files

The following files are configured for optimal Vercel deployment:

### `vercel.json`
- Specifies build configuration
- Sets up security headers
- Configures API routes

### `.vercelignore`
- Excludes backend files and sensitive data
- Reduces deployment size
- Improves build performance

### `package.json` Scripts
- `build`: Standard Next.js build for Vercel
- `build:vercel`: Explicit Vercel build command
- `install:all`: Safe installation without concurrency issues

## 🔍 Troubleshooting

### Common Issues

#### 1. "concurrently: command not found"
**Solution**: Use `pnpm install:all` instead of `pnpm install:concurrent` for deployment

#### 2. Environment Variables Not Working
**Solution**: 
- Ensure all variables start with `NEXT_PUBLIC_` for client-side access
- Redeploy after adding environment variables
- Check variable names for typos

#### 3. API Calls Failing
**Solution**:
- Verify `NEXT_PUBLIC_API_URL` points to your deployed backend
- Ensure backend CORS allows your Vercel domain
- Check backend deployment status

#### 4. Google OAuth Not Working
**Solution**:
- Update Google OAuth redirect URIs with your Vercel domain
- Use production Google OAuth credentials
- Verify `NEXT_PUBLIC_GOOGLE_CLIENT_ID` is correct

### Build Optimization

#### 1. Reduce Bundle Size
```bash
# Analyze bundle
pnpm build && npx @next/bundle-analyzer
```

#### 2. Enable Image Optimization
Already configured in `next.config.ts` for external images

#### 3. Configure Caching
Vercel automatically handles caching for static assets

## 📊 Post-Deployment

### 1. Verify Deployment
- [ ] Frontend loads correctly
- [ ] Environment variables are working
- [ ] API calls reach the backend
- [ ] Google OAuth works
- [ ] Images load properly
- [ ] All pages are accessible

### 2. Set Up Custom Domain (Optional)
1. Go to Project Settings → Domains
2. Add your custom domain
3. Configure DNS records as instructed
4. Update Google OAuth redirect URIs

### 3. Monitor Performance
- Use Vercel Analytics
- Set up error tracking (Sentry)
- Monitor Core Web Vitals

## 🔄 Continuous Deployment

Once connected to GitHub, Vercel will automatically:
- Deploy on every push to main branch
- Create preview deployments for pull requests
- Run build checks before deployment

## 📝 Environment Variables Template

Copy this template to Vercel's environment variables section:

```
NEXT_PUBLIC_API_URL=https://your-backend-api.com/api
NEXT_PUBLIC_FRONTEND_URL=https://your-vercel-app.vercel.app
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-production-google-client-id
NEXT_PUBLIC_APP_NAME=Dazzled
NEXT_PUBLIC_SITE_URL=https://your-vercel-app.vercel.app
NEXT_PUBLIC_DEFAULT_CURRENCY=KES
NODE_ENV=production
```

---

**🎉 Your Dazzled frontend will be live on Vercel!**

Remember to deploy your backend separately and update the API URL accordingly.
