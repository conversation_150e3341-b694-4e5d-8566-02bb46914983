import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Product, ProductDocument } from '../schemas/product.schema';
import { Review, ReviewDocument } from '../schemas/review.schema';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductFiltersDto } from './dto/product-filters.dto';

@Injectable()
export class ProductsService {
  constructor(
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
    @InjectModel(Review.name) private reviewModel: Model<ReviewDocument>,
  ) {}

  async create(createProductDto: CreateProductDto, userId: string): Promise<ProductDocument> {
    const product = new this.productModel({
      ...createProductDto,
      createdBy: userId,
    });
    return product.save();
  }

  async findAll(filters: ProductFiltersDto) {
    const {
      page = 1,
      limit = 12,
      category,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      search,
    } = filters;

    const query: any = { isActive: true };

    // Apply filters
    if (category) query.category = category;



    if (search) {
      query.$text = { $search: search };
    }

    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [products, total] = await Promise.all([
      this.productModel
        .find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('createdBy', 'name')
        .exec(),
      this.productModel.countDocuments(query),
    ]);

    return {
      data: products,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string): Promise<ProductDocument> {
    const product = await this.productModel
      .findById(id)
      .populate('createdBy', 'name')
      .exec();

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    return product;
  }

  async update(id: string, updateProductDto: UpdateProductDto): Promise<ProductDocument> {
    const product = await this.productModel
      .findByIdAndUpdate(id, updateProductDto, { new: true })
      .exec();

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    return product;
  }

  async remove(id: string): Promise<void> {
    const result = await this.productModel.findByIdAndDelete(id).exec();
    if (!result) {
      throw new NotFoundException('Product not found');
    }
  }

  async getFeatured(limit: number = 8): Promise<ProductDocument[]> {
    return this.productModel
      .find({ featured: true, isActive: true })
      .sort({ createdAt: -1 })
      .limit(limit)
      .exec();
  }

  async getRelated(productId: string, limit: number = 4): Promise<ProductDocument[]> {
    const product = await this.findOne(productId);
    
    return this.productModel
      .find({
        _id: { $ne: productId },
        category: product.category,
        isActive: true,
      })
      .sort({ rating: -1 })
      .limit(limit)
      .exec();
  }

  async updateRating(productId: string): Promise<void> {
    const reviews = await this.reviewModel
      .find({ productId, isActive: true })
      .exec();

    if (reviews.length === 0) {
      await this.productModel.findByIdAndUpdate(productId, {
        rating: 0,
        reviewCount: 0,
      });
      return;
    }

    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = totalRating / reviews.length;

    await this.productModel.findByIdAndUpdate(productId, {
      rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
      reviewCount: reviews.length,
    });
  }

  async updateStock(productId: string, quantity: number): Promise<void> {
    const product = await this.findOne(productId);

    if (product.stock < quantity) {
      throw new BadRequestException('Insufficient stock');
    }

    await this.productModel.findByIdAndUpdate(
      productId,
      { $inc: { stock: -quantity } }
    );
  }

  async searchProducts(query: string, limit: number = 10): Promise<ProductDocument[]> {
    return this.productModel
      .find({
        $text: { $search: query },
        isActive: true,
      })
      .sort({ score: { $meta: 'textScore' } })
      .limit(limit)
      .exec();
  }

  async getProductsByCategory(category: string, limit: number = 12): Promise<ProductDocument[]> {
    return this.productModel
      .find({ category, isActive: true })
      .sort({ createdAt: -1 })
      .limit(limit)
      .exec();
  }
}
