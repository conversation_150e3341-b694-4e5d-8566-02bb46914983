import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Product, ProductDocument } from '../schemas/product.schema';
import { Review, ReviewDocument } from '../schemas/review.schema';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductFiltersDto } from './dto/product-filters.dto';

@Injectable()
export class ProductsService {
  constructor(
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
    @InjectModel(Review.name) private reviewModel: Model<ReviewDocument>,
  ) {}

  async create(createProductDto: CreateProductDto, userId: string): Promise<ProductDocument> {
    const product = new this.productModel({
      ...createProductDto,
      createdBy: userId,
    });
    return product.save();
  }

  async findAll(filters: ProductFiltersDto) {
    const {
      page = 1,
      limit = 12,
      category,
      size,
      bedazzlingLevel,
      frameOption,
      priceRange,
      rating,
      inStock,
      featured,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      search,
    } = filters;

    const query: any = { isActive: true };

    // Apply filters
    if (category) query.category = category;
    if (featured !== undefined) query.featured = featured;
    if (rating) query.rating = { $gte: rating };
    if (inStock) query.totalStock = { $gt: 0 };

    if (priceRange && priceRange.length === 2) {
      query.basePrice = { $gte: priceRange[0], $lte: priceRange[1] };
    }

    if (size && size.length > 0) {
      query['variants.size'] = { $in: size };
    }

    if (bedazzlingLevel && bedazzlingLevel.length > 0) {
      query['variants.bedazzlingLevel'] = { $in: bedazzlingLevel };
    }

    if (frameOption && frameOption.length > 0) {
      query['variants.frameOption'] = { $in: frameOption };
    }

    if (search) {
      query.$text = { $search: search };
    }

    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [products, total] = await Promise.all([
      this.productModel
        .find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('createdBy', 'name')
        .exec(),
      this.productModel.countDocuments(query),
    ]);

    return {
      data: products,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string): Promise<ProductDocument> {
    const product = await this.productModel
      .findById(id)
      .populate('createdBy', 'name')
      .exec();

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    return product;
  }

  async update(id: string, updateProductDto: UpdateProductDto): Promise<ProductDocument> {
    const product = await this.productModel
      .findByIdAndUpdate(id, updateProductDto, { new: true })
      .exec();

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    return product;
  }

  async remove(id: string): Promise<void> {
    const result = await this.productModel.findByIdAndDelete(id).exec();
    if (!result) {
      throw new NotFoundException('Product not found');
    }
  }

  async getFeatured(limit: number = 8): Promise<ProductDocument[]> {
    return this.productModel
      .find({ featured: true, isActive: true })
      .sort({ createdAt: -1 })
      .limit(limit)
      .exec();
  }

  async getRelated(productId: string, limit: number = 4): Promise<ProductDocument[]> {
    const product = await this.findOne(productId);
    
    return this.productModel
      .find({
        _id: { $ne: productId },
        category: product.category,
        isActive: true,
      })
      .sort({ rating: -1 })
      .limit(limit)
      .exec();
  }

  async updateRating(productId: string): Promise<void> {
    const reviews = await this.reviewModel
      .find({ productId, isActive: true })
      .exec();

    if (reviews.length === 0) {
      await this.productModel.findByIdAndUpdate(productId, {
        rating: 0,
        reviewCount: 0,
      });
      return;
    }

    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = totalRating / reviews.length;

    await this.productModel.findByIdAndUpdate(productId, {
      rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
      reviewCount: reviews.length,
    });
  }

  async updateStock(productId: string, variantId: string, quantity: number): Promise<void> {
    const product = await this.findOne(productId);

    const variant = product.variants.find(v => v.id === variantId);
    if (!variant) {
      throw new BadRequestException('Variant not found');
    }

    if (variant.stock < quantity) {
      throw new BadRequestException('Insufficient stock');
    }

    await this.productModel.updateOne(
      { _id: productId, 'variants.id': variantId },
      { $inc: { 'variants.$.stock': -quantity } }
    );

    // Update total stock
    const updatedProduct = await this.findOne(productId);
    const totalStock = updatedProduct.variants.reduce((sum, v) => sum + v.stock, 0);

    await this.productModel.findByIdAndUpdate(productId, { totalStock });
  }

  async searchProducts(query: string, limit: number = 10): Promise<ProductDocument[]> {
    return this.productModel
      .find({
        $text: { $search: query },
        isActive: true,
      })
      .sort({ score: { $meta: 'textScore' } })
      .limit(limit)
      .exec();
  }

  async getProductsByCategory(category: string, limit: number = 12): Promise<ProductDocument[]> {
    return this.productModel
      .find({ category, isActive: true })
      .sort({ createdAt: -1 })
      .limit(limit)
      .exec();
  }
}
