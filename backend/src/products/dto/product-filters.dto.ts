import {
  IsOptional,
  IsString,
  IsArray,
  IsNumber,
  IsBoolean,
  IsEnum,
  Min,
  Max,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';

export class ProductFiltersDto {
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @Min(1)
  @Max(50)
  limit?: number = 12;

  @IsOptional()
  @IsEnum(['pre-made', 'custom'])
  category?: string;

  @IsOptional()
  @IsArray()
  @IsEnum(['small', 'medium', 'large', 'extra-large'], { each: true })
  @Transform(({ value }) => Array.isArray(value) ? value : [value])
  size?: string[];

  @IsOptional()
  @IsArray()
  @IsEnum(['light', 'medium', 'heavy', 'premium'], { each: true })
  @Transform(({ value }) => Array.isArray(value) ? value : [value])
  bedazzlingLevel?: string[];

  @IsOptional()
  @IsArray()
  @IsEnum(['basic', 'premium', 'luxury'], { each: true })
  @Transform(({ value }) => Array.isArray(value) ? value : [value])
  frameOption?: string[];

  @IsOptional()
  @IsArray()
  @Type(() => Number)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map(Number);
    }
    return value;
  })
  priceRange?: [number, number];

  @IsOptional()
  @Type(() => Number)
  @Min(0)
  @Max(5)
  rating?: number;

  @IsOptional()
  @Type(() => Boolean)
  inStock?: boolean;

  @IsOptional()
  @Type(() => Boolean)
  featured?: boolean;

  @IsOptional()
  @IsEnum(['name', 'price', 'rating', 'createdAt', 'popular'])
  sortBy?: string = 'createdAt';

  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: string = 'desc';

  @IsOptional()
  @IsString()
  search?: string;
}
