import {
  <PERSON><PERSON><PERSON>,
  <PERSON>Array,
  <PERSON>N<PERSON>ber,
  IsBoolean,
  IsEnum,
  <PERSON><PERSON><PERSON>al,
  Min,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateProductDto {
  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsString()
  @IsEnum(['portraits', 'accessories', 'custom'])
  category: string;

  @IsOptional()
  @IsString()
  image?: string;

  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseFloat(value))
  basePrice: number;

  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseInt(value))
  stock: number;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  featured?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return JSON.parse(value);
    }
    return value;
  })
  tags?: string[];
}
