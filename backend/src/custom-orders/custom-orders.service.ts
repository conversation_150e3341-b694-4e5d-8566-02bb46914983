import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CustomOrderRequest, CustomOrderRequestDocument } from '../schemas/order.schema';
import { CreateCustomOrderDto } from './dto/create-custom-order.dto';
import { UpdateCustomOrderDto } from './dto/update-custom-order.dto';
import { CustomOrderFiltersDto } from './dto/custom-order-filters.dto';

@Injectable()
export class CustomOrdersService {
  constructor(
    @InjectModel(CustomOrderRequest.name) 
    private customOrderModel: Model<CustomOrderRequestDocument>,
  ) {}

  async create(createCustomOrderDto: CreateCustomOrderDto, userId: string): Promise<CustomOrderRequestDocument> {
    const customOrder = new this.customOrderModel({
      ...createCustomOrderDto,
      userId,
      status: 'pending',
    });
    return customOrder.save();
  }

  async findAll(filters: CustomOrderFiltersDto) {
    const {
      page = 1,
      limit = 10,
      status,
      userId,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = filters;

    const query: any = {};

    // Apply filters
    if (status) query.status = status;
    if (userId) query.userId = userId;

    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [orders, total] = await Promise.all([
      this.customOrderModel
        .find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('userId', 'name email')
        .exec(),
      this.customOrderModel.countDocuments(query),
    ]);

    return {
      data: orders,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, userId: string, userRole: string): Promise<CustomOrderRequestDocument> {
    const order = await this.customOrderModel
      .findById(id)
      .populate('userId', 'name email')
      .exec();

    if (!order) {
      throw new NotFoundException('Custom order not found');
    }

    // Regular users can only view their own orders
    if (userRole !== 'admin' && order.userId.toString() !== userId) {
      throw new ForbiddenException('Access denied');
    }

    return order;
  }

  async update(id: string, updateCustomOrderDto: UpdateCustomOrderDto): Promise<CustomOrderRequestDocument> {
    const order = await this.customOrderModel
      .findByIdAndUpdate(id, updateCustomOrderDto, { new: true })
      .populate('userId', 'name email')
      .exec();

    if (!order) {
      throw new NotFoundException('Custom order not found');
    }

    return order;
  }

  async updateStatus(
    id: string, 
    statusUpdate: { status: string; adminNotes?: string; quote?: number }
  ): Promise<CustomOrderRequestDocument> {
    const order = await this.customOrderModel
      .findByIdAndUpdate(id, statusUpdate, { new: true })
      .populate('userId', 'name email')
      .exec();

    if (!order) {
      throw new NotFoundException('Custom order not found');
    }

    return order;
  }

  async remove(id: string): Promise<void> {
    const result = await this.customOrderModel.findByIdAndDelete(id).exec();
    if (!result) {
      throw new NotFoundException('Custom order not found');
    }
  }

  async getStats() {
    const [
      totalOrders,
      pendingOrders,
      completedOrders,
      totalRevenue,
    ] = await Promise.all([
      this.customOrderModel.countDocuments(),
      this.customOrderModel.countDocuments({ status: 'pending' }),
      this.customOrderModel.countDocuments({ status: 'completed' }),
      this.customOrderModel.aggregate([
        { $match: { status: 'completed', quote: { $exists: true } } },
        { $group: { _id: null, total: { $sum: '$quote' } } }
      ]),
    ]);

    return {
      totalOrders,
      pendingOrders,
      completedOrders,
      totalRevenue: totalRevenue[0]?.total || 0,
    };
  }

  async getRecentOrders(limit: number = 5): Promise<CustomOrderRequestDocument[]> {
    return this.customOrderModel
      .find()
      .sort({ createdAt: -1 })
      .limit(limit)
      .populate('userId', 'name email')
      .exec();
  }
}
