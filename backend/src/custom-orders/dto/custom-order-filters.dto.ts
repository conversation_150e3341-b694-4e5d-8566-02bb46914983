import { IsOptional, IsString, IsN<PERSON>ber, IsEnum, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class CustomOrderFiltersDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number;

  @IsOptional()
  @IsEnum(['pending', 'quoted', 'approved', 'in-progress', 'completed', 'cancelled'])
  status?: string;

  @IsOptional()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsEnum(['createdAt', 'updatedAt', 'budget', 'quote'])
  sortBy?: string;

  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: string;
}
