import { PartialType } from '@nestjs/mapped-types';
import { IsEnum, IsNumber, IsOptional, IsString, Min } from 'class-validator';
import { CreateCustomOrderDto } from './create-custom-order.dto';

export class UpdateCustomOrderDto extends PartialType(CreateCustomOrderDto) {
  @IsOptional()
  @IsEnum(['pending', 'quoted', 'approved', 'in-progress', 'completed', 'cancelled'])
  status?: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  quote?: number;

  @IsOptional()
  @IsString()
  adminNotes?: string;
}
