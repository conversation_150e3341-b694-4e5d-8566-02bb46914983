import { IsString, IsEmail, IsEnum, IsNumber, IsOptional, IsArray, IsBoolean, Min } from 'class-validator';

export class CreateCustomOrderDto {
  @IsString()
  customerName: string;

  @IsEmail()
  email: string;

  @IsString()
  phone: string;

  @IsString()
  description: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  referenceImages?: string[];

  @IsEnum(['small', 'medium', 'large', 'extra-large'])
  size: string;

  @IsEnum(['light', 'medium', 'heavy', 'premium'])
  bedazzlingLevel: string;

  @IsEnum(['basic', 'premium', 'luxury'])
  frameOption: string;

  @IsOptional()
  @IsEnum(['simple', 'detailed', 'intricate'])
  complexity?: string;

  @IsOptional()
  @IsNumber()
  @Min(1)
  quantity?: number;

  @IsOptional()
  @IsBoolean()
  rushOrder?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  additionalFeatures?: string[];

  @IsNumber()
  @Min(0)
  budget: number;

  @IsOptional()
  deadline?: Date;

  @IsOptional()
  @IsNumber()
  @Min(0)
  estimatedPrice?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  estimatedDelivery?: number;

  @IsOptional()
  @IsString()
  notes?: string;
}
