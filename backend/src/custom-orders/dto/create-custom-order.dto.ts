import { IsString, IsEmail, IsEnum, IsNumber, IsOptional, IsArray, Min } from 'class-validator';

export class CreateCustomOrderDto {
  @IsString()
  customerName: string;

  @IsEmail()
  email: string;

  @IsString()
  description: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  referenceImages?: string[];

  @IsEnum(['small', 'medium', 'large', 'extra-large'])
  size: string;

  @IsNumber()
  @Min(0)
  budget: number;
}
