import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { CustomOrdersService } from './custom-orders.service';
import { CreateCustomOrderDto } from './dto/create-custom-order.dto';
import { UpdateCustomOrderDto } from './dto/update-custom-order.dto';
import { CustomOrderFiltersDto } from './dto/custom-order-filters.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { GetUser } from '../auth/decorators/get-user.decorator';

@Controller('custom-orders')
export class CustomOrdersController {
  constructor(private readonly customOrdersService: CustomOrdersService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FilesInterceptor('referenceImages', 10))
  async create(
    @Body() createCustomOrderDto: CreateCustomOrderDto,
    @GetUser() user: any,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    if (files && files.length > 0) {
      createCustomOrderDto.referenceImages = files.map(file => `/uploads/custom-orders/${file.filename}`);
    }

    return this.customOrdersService.create(createCustomOrderDto, user._id.toString());
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  async findAll(@GetUser() user: any, @Query() filters: CustomOrderFiltersDto) {
    // Regular users can only see their own orders
    if (user.role !== 'admin') {
      filters.userId = user._id.toString();
    }
    return this.customOrdersService.findAll(filters);
  }

  @Get('my-orders')
  @UseGuards(JwtAuthGuard)
  async getMyOrders(@GetUser() user: any, @Query() filters: CustomOrderFiltersDto) {
    filters.userId = user._id.toString();
    return this.customOrdersService.findAll(filters);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  async findOne(@Param('id') id: string, @GetUser() user: any) {
    return this.customOrdersService.findOne(id, user._id.toString(), user.role);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async update(
    @Param('id') id: string,
    @Body() updateCustomOrderDto: UpdateCustomOrderDto,
  ) {
    return this.customOrdersService.update(id, updateCustomOrderDto);
  }

  @Patch(':id/status')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async updateStatus(
    @Param('id') id: string,
    @Body() statusUpdate: { status: string; adminNotes?: string; quote?: number },
  ) {
    return this.customOrdersService.updateStatus(id, statusUpdate);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async remove(@Param('id') id: string) {
    return this.customOrdersService.remove(id);
  }

  // Admin-only endpoints
  @Get('admin/stats')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async getStats() {
    return this.customOrdersService.getStats();
  }

  @Get('admin/pending')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async getPendingOrders(@Query() filters: CustomOrderFiltersDto) {
    filters.status = 'pending';
    return this.customOrdersService.findAll(filters);
  }
}
