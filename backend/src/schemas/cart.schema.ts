import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type CartDocument = Cart & Document;

@Schema({ _id: false })
export class CartItem {
  @Prop({ required: true })
  id: string;

  @Prop({ type: Types.ObjectId, ref: 'Product', required: true })
  productId: Types.ObjectId;

  @Prop({ required: true })
  variantId: string;

  @Prop({ required: true, min: 1 })
  quantity: number;

  @Prop({ default: Date.now })
  addedAt: Date;
}

@Schema({ timestamps: true })
export class Cart {
  @Prop({ type: Types.ObjectId, ref: 'User' })
  userId?: Types.ObjectId;

  @Prop()
  sessionId?: string;

  @Prop({ type: [CartItem], default: [] })
  items: CartItem[];

  @Prop({ min: 0, default: 0 })
  total: number;

  @Prop({ default: Date.now })
  lastActivity: Date;
}

export const CartSchema = SchemaFactory.createForClass(Cart);

// Indexes
CartSchema.index({ userId: 1 });
CartSchema.index({ sessionId: 1 });
CartSchema.index({ lastActivity: 1 });
CartSchema.index({ 'items.productId': 1 });

// TTL index for guest carts (expire after 30 days)
CartSchema.index({ lastActivity: 1 }, { expireAfterSeconds: 2592000, partialFilterExpression: { userId: { $exists: false } } });
