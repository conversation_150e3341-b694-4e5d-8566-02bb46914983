import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ReviewDocument = Review & Document;

@Schema({ timestamps: true })
export class Review {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Product', required: true })
  productId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Order', required: true })
  orderId: Types.ObjectId;

  @Prop({ required: true, min: 1, max: 5 })
  rating: number;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  comment: string;

  @Prop({ type: [String], default: [] })
  images: string[];

  @Prop({ default: false })
  verified: boolean;

  @Prop({ min: 0, default: 0 })
  helpful: number;

  @Prop({ default: true })
  isActive: boolean;

  @Prop()
  adminResponse?: string;

  @Prop()
  adminResponseAt?: Date;
}

export const ReviewSchema = SchemaFactory.createForClass(Review);

// Indexes
ReviewSchema.index({ userId: 1 });
ReviewSchema.index({ productId: 1 });
ReviewSchema.index({ orderId: 1 });
ReviewSchema.index({ rating: 1 });
ReviewSchema.index({ verified: 1 });
ReviewSchema.index({ createdAt: -1 });
ReviewSchema.index({ isActive: 1 });

// Compound indexes
ReviewSchema.index({ productId: 1, rating: -1 });
ReviewSchema.index({ userId: 1, productId: 1 }, { unique: true });
