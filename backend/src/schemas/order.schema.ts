import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type OrderDocument = Order & Document;



@Schema({ _id: false })
export class OrderItem {
  @Prop({ required: true })
  productId: string;

  @Prop({ required: true })
  productName: string;

  @Prop({ required: true, min: 1 })
  quantity: number;

  @Prop({ required: true, min: 0 })
  price: number;
}

@Schema({ timestamps: true })
export class Order {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ required: true })
  customerName: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  phone: string;

  @Prop({ type: [OrderItem], required: true })
  items: OrderItem[];

  @Prop({ required: true, min: 0 })
  total: number;

  @Prop({
    enum: ['pending', 'processing', 'shipped', 'delivered', 'cancelled'],
    default: 'pending',
  })
  status: string;

  @Prop({ required: true })
  shippingAddress: string;

  @Prop({ enum: ['mpesa', 'cash'], required: true })
  paymentMethod: string;

  @Prop()
  adminNotes?: string;
}

export const OrderSchema = SchemaFactory.createForClass(Order);

// Custom order request schema
@Schema({ timestamps: true })
export class CustomOrderRequest {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ required: true })
  customerName: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  description: string;

  @Prop({ type: [String], default: [] })
  referenceImages: string[];

  @Prop({ enum: ['small', 'medium', 'large', 'extra-large'], required: true })
  size: string;

  @Prop({ required: true, min: 0 })
  budget: number;

  @Prop({
    enum: ['pending', 'quoted', 'approved', 'in-progress', 'completed', 'cancelled'],
    default: 'pending',
  })
  status: string;

  @Prop({ min: 0 })
  quote?: number;

  @Prop()
  notes?: string;

  @Prop()
  adminNotes?: string;
}

export type CustomOrderRequestDocument = CustomOrderRequest & Document;
export const CustomOrderRequestSchema = SchemaFactory.createForClass(CustomOrderRequest);

// Indexes
OrderSchema.index({ userId: 1 });
OrderSchema.index({ orderNumber: 1 });
OrderSchema.index({ status: 1 });
OrderSchema.index({ createdAt: -1 });
OrderSchema.index({ 'items.productId': 1 });
OrderSchema.index({ total: 1 });

CustomOrderRequestSchema.index({ userId: 1 });
CustomOrderRequestSchema.index({ status: 1 });
CustomOrderRequestSchema.index({ createdAt: -1 });
