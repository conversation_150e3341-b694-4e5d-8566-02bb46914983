import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type OrderDocument = Order & Document;

@Schema({ _id: false })
export class ShippingAddress {
  @Prop({ required: true })
  firstName: string;

  @Prop({ required: true })
  lastName: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  phone: string;

  @Prop({ required: true })
  address: string;

  @Prop({ required: true })
  city: string;

  @Prop({ required: true })
  state: string;

  @Prop({ required: true })
  zipCode: string;

  @Prop({ required: true })
  country: string;
}

@Schema({ _id: false })
export class OrderItem {
  @Prop({ required: true })
  productId: string;

  @Prop({ required: true })
  variantId: string;

  @Prop({ required: true, min: 1 })
  quantity: number;

  @Prop({ required: true, min: 0 })
  price: number;

  @Prop({ required: true })
  productName: string;

  @Prop()
  productImage?: string;

  @Prop({
    type: {
      size: { type: String, required: true },
      bedazzlingLevel: { type: String, required: true },
      frameOption: { type: String, required: true },
      complexity: { type: String, required: false }
    },
    required: true
  })
  variantDetails: {
    size: string;
    bedazzlingLevel: string;
    frameOption: string;
    complexity?: string;
  };
}

@Schema({ timestamps: true })
export class Order {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ required: true, unique: true })
  orderNumber: string;

  @Prop({ type: [OrderItem], required: true })
  items: OrderItem[];

  @Prop({ required: true, min: 0 })
  subtotal: number;

  @Prop({ required: true, min: 0, default: 0 })
  shipping: number;

  @Prop({ required: true, min: 0, default: 0 })
  tax: number;

  @Prop({ required: true, min: 0 })
  total: number;

  @Prop({
    enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'],
    default: 'pending',
  })
  status: string;

  @Prop({ type: ShippingAddress, required: true })
  shippingAddress: ShippingAddress;

  @Prop()
  notes?: string;

  @Prop()
  trackingNumber?: string;

  @Prop()
  estimatedDelivery?: Date;

  @Prop()
  deliveredAt?: Date;

  @Prop()
  cancelledAt?: Date;

  @Prop()
  cancellationReason?: string;

  @Prop({ type: [String], default: [] })
  statusHistory: string[];
}

export const OrderSchema = SchemaFactory.createForClass(Order);

// Custom order request schema
@Schema({ timestamps: true })
export class CustomOrderRequest {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ required: true })
  customerName: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  phone: string;

  @Prop({ required: true })
  description: string;

  @Prop({ type: [String], default: [] })
  referenceImages: string[];

  @Prop({ enum: ['small', 'medium', 'large', 'extra-large'], required: true })
  size: string;

  @Prop({ enum: ['light', 'medium', 'heavy', 'premium'], required: true })
  bedazzlingLevel: string;

  @Prop({ enum: ['basic', 'premium', 'luxury'], required: true })
  frameOption: string;

  @Prop({ enum: ['simple', 'detailed', 'intricate'] })
  complexity?: string;

  @Prop({ min: 1, default: 1 })
  quantity: number;

  @Prop({ default: false })
  rushOrder: boolean;

  @Prop({ type: [String], default: [] })
  additionalFeatures: string[];

  @Prop({ required: true, min: 0 })
  budget: number;

  @Prop()
  deadline?: Date;

  @Prop({ min: 0 })
  estimatedPrice?: number;

  @Prop({ min: 1 })
  estimatedDelivery?: number;

  @Prop({
    enum: ['pending', 'quoted', 'approved', 'in-progress', 'completed', 'cancelled'],
    default: 'pending',
  })
  status: string;

  @Prop({ min: 0 })
  quote?: number;

  @Prop()
  notes?: string;

  @Prop()
  adminNotes?: string;
}

export type CustomOrderRequestDocument = CustomOrderRequest & Document;
export const CustomOrderRequestSchema = SchemaFactory.createForClass(CustomOrderRequest);

// Indexes
OrderSchema.index({ userId: 1 });
OrderSchema.index({ orderNumber: 1 });
OrderSchema.index({ status: 1 });
OrderSchema.index({ createdAt: -1 });
OrderSchema.index({ 'items.productId': 1 });
OrderSchema.index({ total: 1 });

CustomOrderRequestSchema.index({ userId: 1 });
CustomOrderRequestSchema.index({ status: 1 });
CustomOrderRequestSchema.index({ createdAt: -1 });
