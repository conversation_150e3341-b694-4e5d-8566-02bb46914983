import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ProductDocument = Product & Document;

@Schema({ _id: false })
export class ProductVariant {
  @Prop({ required: true })
  id: string;

  @Prop({ enum: ['small', 'medium', 'large', 'extra-large'], required: true })
  size: string;

  @Prop({ enum: ['light', 'medium', 'heavy', 'premium'], required: true })
  bedazzlingLevel: string;

  @Prop({ enum: ['basic', 'premium', 'luxury'], required: true })
  frameOption: string;

  @Prop({ enum: ['simple', 'detailed', 'intricate'] })
  complexity?: string;

  @Prop({ required: true, min: 0 })
  price: number;

  @Prop({ required: true, min: 0, default: 0 })
  stock: number;
}

@Schema({ timestamps: true })
export class Product {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop({ enum: ['pre-made', 'custom'], required: true })
  category: string;

  @Prop({ type: [String], default: [] })
  images: string[];

  @Prop({ type: [ProductVariant], default: [] })
  variants: ProductVariant[];

  @Prop({ required: true, min: 0 })
  basePrice: number;

  @Prop({ default: false })
  featured: boolean;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ min: 0, max: 5, default: 0 })
  rating: number;

  @Prop({ min: 0, default: 0 })
  reviewCount: number;

  @Prop({ default: true })
  isActive: boolean;

  @Prop()
  sku?: string;

  @Prop({ min: 0, default: 0 })
  totalStock: number;

  @Prop({ min: 0, default: 0 })
  soldCount: number;

  @Prop()
  metaTitle?: string;

  @Prop()
  metaDescription?: string;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  createdBy: Types.ObjectId;
}

export const ProductSchema = SchemaFactory.createForClass(Product);

// Indexes
ProductSchema.index({ name: 'text', description: 'text', tags: 'text' });
ProductSchema.index({ category: 1 });
ProductSchema.index({ featured: 1 });
ProductSchema.index({ rating: -1 });
ProductSchema.index({ createdAt: -1 });
ProductSchema.index({ basePrice: 1 });
ProductSchema.index({ isActive: 1 });
ProductSchema.index({ 'variants.size': 1 });
ProductSchema.index({ 'variants.bedazzlingLevel': 1 });
ProductSchema.index({ 'variants.frameOption': 1 });
