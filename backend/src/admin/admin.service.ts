import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User, UserDocument } from '../schemas/user.schema';
import { Product, ProductDocument } from '../schemas/product.schema';
import { CustomOrderRequest, CustomOrderRequestDocument } from '../schemas/order.schema';
import { UserFiltersDto } from './dto/user-filters.dto';

@Injectable()
export class AdminService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
    @InjectModel(CustomOrderRequest.name) private customOrderModel: Model<CustomOrderRequestDocument>,
  ) {}

  async getUsers(filters: UserFiltersDto) {
    const {
      page = 1,
      limit = 10,
      role,
      isActive,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      search,
    } = filters;

    const query: any = {};

    // Apply filters
    if (role) query.role = role;
    if (isActive !== undefined) query.isActive = isActive;
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }

    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      this.userModel
        .find(query)
        .select('-password') // Exclude password field
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.userModel.countDocuments(query),
    ]);

    return {
      data: users,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getUserById(id: string): Promise<UserDocument> {
    const user = await this.userModel
      .findById(id)
      .select('-password')
      .exec();

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async updateUserRole(id: string, role: string): Promise<UserDocument> {
    const user = await this.userModel
      .findByIdAndUpdate(id, { role }, { new: true })
      .select('-password')
      .exec();

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async updateUserStatus(id: string, isActive: boolean): Promise<UserDocument> {
    const user = await this.userModel
      .findByIdAndUpdate(id, { isActive }, { new: true })
      .select('-password')
      .exec();

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async getAdminStats() {
    const [
      totalUsers,
      totalAdmins,
      totalCustomers,
      activeUsers,
      totalProducts,
      totalOrders,
      pendingOrders,
      completedOrders,
    ] = await Promise.all([
      this.userModel.countDocuments(),
      this.userModel.countDocuments({ role: 'admin' }),
      this.userModel.countDocuments({ role: 'buyer' }),
      this.userModel.countDocuments({ isActive: true }),
      this.productModel.countDocuments({ isActive: true }),
      this.customOrderModel.countDocuments(),
      this.customOrderModel.countDocuments({ status: 'pending' }),
      this.customOrderModel.countDocuments({ status: 'completed' }),
    ]);

    // Calculate total revenue from completed orders with quotes
    const revenueResult = await this.customOrderModel.aggregate([
      { $match: { status: 'completed', quote: { $exists: true } } },
      { $group: { _id: null, total: { $sum: '$quote' } } }
    ]);

    const totalRevenue = revenueResult[0]?.total || 0;

    return {
      users: {
        total: totalUsers,
        admins: totalAdmins,
        customers: totalCustomers,
        active: activeUsers,
      },
      products: {
        total: totalProducts,
      },
      orders: {
        total: totalOrders,
        pending: pendingOrders,
        completed: completedOrders,
      },
      revenue: {
        total: totalRevenue,
      },
    };
  }
}
