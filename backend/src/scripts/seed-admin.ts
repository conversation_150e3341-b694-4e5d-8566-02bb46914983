import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import * as bcrypt from 'bcryptjs';
import { Model } from 'mongoose';
import { User, UserDocument } from '../schemas/user.schema';
import { getModelToken } from '@nestjs/mongoose';

async function seedAdmin() {
  const app = await NestFactory.createApplicationContext(AppModule);

  try {
    const userModel = app.get<Model<UserDocument>>(getModelToken(User.name));
    
    // Check if admin already exists
    const existingAdmin = await userModel.findOne({ 
      email: '<EMAIL>',
    });
    
    if (existingAdmin) {
      console.log('Admin user already exists');
      return;
    }
    
    // Create admin user
    const hashedPassword = await bcrypt.hash('123456789', 12);
    
    const adminUser = new userModel({
      name: 'Bedazzled Admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      isActive: true,
      isEmailVerified: true,
      phone: '+254799553438',
      defaultAddress: {
        firstName: 'Bedazzled',
        lastName: 'Admin',
        address: 'Westlands Shopping Centre',
        city: 'Nairobi',
        state: 'Nairobi County',
        zipCode: '00100',
        country: 'Kenya',
      },
    });
    
    await adminUser.save();
    
    console.log('✅ Admin user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: 123456789');
    console.log('🛡️  Role: admin');
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
  } finally {
    await app.close();
  }
}

// Run the seeder
seedAdmin().catch(console.error);
