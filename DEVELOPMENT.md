# Development Guide - Bedazzled E-commerce Platform

This guide covers the development workflow, architecture decisions, and best practices for the Bedazzled platform.

## 🏗️ Architecture Overview

### Frontend Architecture (Next.js 15)
```
src/
├── app/                    # App Router pages
│   ├── (auth)/            # Auth group routes
│   ├── admin/             # Admin dashboard
│   ├── products/          # Product pages
│   └── api/               # API routes (if needed)
├── components/            # Reusable components
│   ├── ui/               # Base UI components (shadcn/ui)
│   ├── layout/           # Layout components
│   ├── products/         # Product-specific components
│   └── admin/            # Admin components
├── contexts/             # React contexts
├── lib/                  # Utility functions
├── types/                # TypeScript definitions
└── styles/               # Global styles
```

### Backend Architecture (NestJS)
```
backend/src/
├── auth/                 # Authentication module
├── products/             # Products module
├── orders/               # Product orders module
├── custom-orders/        # Custom orders module
├── admin/                # Admin module
├── schemas/              # MongoDB schemas
├── common/               # Shared utilities
└── main.ts               # Application entry point
```

## 🛠️ Development Workflow

### 1. Setting Up Development Environment

```bash
# Clone repository
git clone https://github.com/your-username/bedazzled.git
cd bedazzled

# Install dependencies for both frontend and backend
pnpm setup

# Start development servers
pnpm dev
```

### 2. Environment Configuration

**Frontend (.env.local):**
```env
NEXT_PUBLIC_API_URL=http://localhost:3002
NEXT_PUBLIC_ENABLE_WISHLIST=false
NEXT_PUBLIC_SITE_URL=http://localhost:3001
```

**Backend (.env):**
```env
NODE_ENV=development
PORT=3002
MONGODB_URI=mongodb://localhost:27017/bedazzled
JWT_SECRET=your-development-jwt-secret
JWT_EXPIRES_IN=7d
CORS_ORIGIN=http://localhost:3001
```

### 3. Development Scripts

```bash
# Frontend only
pnpm dev:frontend

# Backend only
pnpm dev:backend

# Both concurrently
pnpm dev

# Build for production
pnpm build:full

# Run tests
pnpm test:full

# Lint code
pnpm lint:full

# Format code
pnpm format
```

## 🎨 Design System

### Color Palette
```css
/* Primary Colors */
--background: 0 0% 100%;           /* White */
--foreground: 222.2 84% 4.9%;      /* Dark text */
--muted: 210 40% 96%;              /* Light gray */
--muted-foreground: 215.4 16.3% 46.9%; /* Medium gray */

/* Accent Colors (Rhinestone/Bedazzled) */
--accent: 43 96% 56%;              /* Gold */
--highlight: 280 100% 70%;         /* Purple/Pink */
--accent-foreground: 210 40% 98%;  /* Light text on accent */

/* Semantic Colors */
--destructive: 0 84.2% 60.2%;      /* Red for errors */
--success: 142 76% 36%;            /* Green for success */
--warning: 38 92% 50%;             /* Orange for warnings */
```

### Typography Scale
```css
/* Font Sizes */
text-xs: 0.75rem;     /* 12px */
text-sm: 0.875rem;    /* 14px */
text-base: 1rem;      /* 16px */
text-lg: 1.125rem;    /* 18px */
text-xl: 1.25rem;     /* 20px */
text-2xl: 1.5rem;     /* 24px */
text-3xl: 1.875rem;   /* 30px */
text-4xl: 2.25rem;    /* 36px */
```

### Component Guidelines

**Buttons:**
```tsx
// Primary action
<Button>Add to Cart</Button>

// Secondary action
<Button variant="outline">View Details</Button>

// Destructive action
<Button variant="destructive">Delete</Button>

// Loading state
<Button disabled={loading}>
  {loading ? 'Loading...' : 'Submit'}
</Button>
```

**Cards:**
```tsx
<Card>
  <CardHeader>
    <CardTitle>Product Name</CardTitle>
    <CardDescription>Product description</CardDescription>
  </CardHeader>
  <CardContent>
    {/* Card content */}
  </CardContent>
</Card>
```

## 🔧 Development Best Practices

### 1. Code Organization

**Component Structure:**
```tsx
'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/auth-context';

interface ComponentProps {
  // Props interface
}

export function Component({ prop }: ComponentProps) {
  // Hooks
  const { user } = useAuth();
  const [state, setState] = useState();

  // Effects
  useEffect(() => {
    // Effect logic
  }, []);

  // Event handlers
  const handleClick = () => {
    // Handler logic
  };

  // Render
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      {/* Component JSX */}
    </motion.div>
  );
}
```

### 2. State Management

**Context Pattern:**
```tsx
// contexts/example-context.tsx
interface ExampleContextType {
  data: Data[];
  loading: boolean;
  addData: (data: Data) => Promise<void>;
}

const ExampleContext = createContext<ExampleContextType | undefined>(undefined);

export function useExample() {
  const context = useContext(ExampleContext);
  if (!context) {
    throw new Error('useExample must be used within ExampleProvider');
  }
  return context;
}

export function ExampleProvider({ children }: { children: React.ReactNode }) {
  // Provider implementation
  return (
    <ExampleContext.Provider value={value}>
      {children}
    </ExampleContext.Provider>
  );
}
```

### 3. API Integration

**Frontend API Calls:**
```tsx
const fetchData = async () => {
  try {
    setLoading(true);
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/endpoint`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      setData(data);
    } else {
      throw new Error('Failed to fetch data');
    }
  } catch (error) {
    console.error('Error:', error);
    setError(error.message);
  } finally {
    setLoading(false);
  }
};
```

**Backend Controller:**
```tsx
@Controller('endpoint')
export class ExampleController {
  constructor(private readonly exampleService: ExampleService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  async findAll(@Request() req) {
    return this.exampleService.findAll(req.user.userId);
  }

  @Post()
  @UseGuards(JwtAuthGuard)
  async create(@Body() createDto: CreateDto, @Request() req) {
    return this.exampleService.create(createDto, req.user.userId);
  }
}
```

### 4. Error Handling

**Frontend Error Boundaries:**
```tsx
import { ErrorBoundary } from '@/components/ui/error-boundary';

function App() {
  return (
    <ErrorBoundary>
      <YourComponent />
    </ErrorBoundary>
  );
}
```

**Backend Error Handling:**
```tsx
@Injectable()
export class ExampleService {
  async findOne(id: string) {
    try {
      const item = await this.model.findById(id);
      if (!item) {
        throw new NotFoundException('Item not found');
      }
      return item;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to fetch item');
    }
  }
}
```

## 🧪 Testing Strategy

### Frontend Testing
```bash
# Unit tests with Jest
pnpm test

# Watch mode
pnpm test:watch

# Coverage report
pnpm test:coverage
```

**Component Testing:**
```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '@/components/ui/button';

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### Backend Testing
```bash
# Unit tests
cd backend && pnpm test

# E2E tests
cd backend && pnpm test:e2e

# Coverage
cd backend && pnpm test:cov
```

**Service Testing:**
```tsx
describe('ProductsService', () => {
  let service: ProductsService;
  let model: Model<Product>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        ProductsService,
        {
          provide: getModelToken(Product.name),
          useValue: mockModel,
        },
      ],
    }).compile();

    service = module.get<ProductsService>(ProductsService);
    model = module.get<Model<Product>>(getModelToken(Product.name));
  });

  it('should create a product', async () => {
    const createDto = { name: 'Test Product', price: 100 };
    const result = await service.create(createDto);
    
    expect(result).toBeDefined();
    expect(result.name).toBe(createDto.name);
  });
});
```

## 🚀 Performance Optimization

### Frontend Optimization
```tsx
// Lazy loading components
const LazyComponent = lazy(() => import('./LazyComponent'));

// Memoization
const MemoizedComponent = memo(({ data }) => {
  return <div>{data}</div>;
});

// Image optimization
import Image from 'next/image';

<Image
  src="/product.jpg"
  alt="Product"
  width={300}
  height={300}
  priority={false}
  placeholder="blur"
/>
```

### Backend Optimization
```tsx
// Database indexing
@Schema()
export class Product {
  @Prop({ index: true })
  category: string;

  @Prop({ index: true })
  featured: boolean;
}

// Pagination
async findAll(page: number = 1, limit: number = 10) {
  const skip = (page - 1) * limit;
  
  const [products, total] = await Promise.all([
    this.productModel.find().skip(skip).limit(limit),
    this.productModel.countDocuments(),
  ]);

  return {
    data: products,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  };
}
```

## 🔍 Debugging

### Frontend Debugging
```tsx
// React Developer Tools
// Next.js debugging
console.log('Debug info:', { user, cart, products });

// Network debugging
const response = await fetch('/api/products');
console.log('Response:', response.status, await response.json());
```

### Backend Debugging
```tsx
// NestJS Logger
import { Logger } from '@nestjs/common';

@Injectable()
export class ProductsService {
  private readonly logger = new Logger(ProductsService.name);

  async findAll() {
    this.logger.log('Fetching all products');
    // Service logic
  }
}
```

## 📝 Code Style Guidelines

### TypeScript
- Use strict TypeScript configuration
- Define interfaces for all data structures
- Use proper typing for function parameters and returns
- Avoid `any` type

### React
- Use functional components with hooks
- Implement proper error boundaries
- Use React.memo for performance optimization
- Follow React best practices

### NestJS
- Use decorators appropriately
- Implement proper validation with DTOs
- Use guards for authentication/authorization
- Follow NestJS architectural patterns

## 🔄 Git Workflow

```bash
# Feature development
git checkout -b feature/new-feature
git add .
git commit -m "feat: add new feature"
git push origin feature/new-feature

# Create pull request
# After review and approval, merge to main
```

### Commit Message Convention
```
feat: add new feature
fix: resolve bug
docs: update documentation
style: format code
refactor: restructure code
test: add tests
chore: update dependencies
```

## 📚 Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [NestJS Documentation](https://docs.nestjs.com)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Framer Motion Documentation](https://www.framer.com/motion)
- [MongoDB Documentation](https://docs.mongodb.com)

For questions or issues, please refer to the main README.md or create an issue in the repository.
