# 🚀 Bedazzled Platform Setup Guide

This guide will walk you through setting up all the necessary credentials and services to make the Bedazzled platform fully functional.

## 📋 Prerequisites

- Node.js 18+ installed
- MongoDB installed locally or MongoDB Atlas account
- Google Cloud Console account
- Email account for SMTP (Gmail recommended for development)

## 🔧 Step-by-Step Setup

### 1. Database Setup (MongoDB)

#### Option A: Local MongoDB
```bash
# Install MongoDB locally (macOS with Homebrew)
brew tap mongodb/brew
brew install mongodb-community

# Start MongoDB
brew services start mongodb-community

# Your connection string will be:
# MONGODB_URI=mongodb://localhost:27017/bedazzled
```

#### Option B: MongoDB Atlas (Cloud)
1. Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a free account and cluster
3. Create a database user
4. Whitelist your IP address (or use 0.0.0.0/0 for development)
5. Get your connection string:
   ```
   MONGODB_URI=mongodb+srv://username:<EMAIL>/bedazzled
   ```

### 2. Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the following APIs:
   - Google+ API
   - Google OAuth2 API
4. Go to **Credentials** → **Create Credentials** → **OAuth 2.0 Client IDs**
5. Configure OAuth consent screen:
   - Application name: "Bedazzled"
   - Authorized domains: `localhost` (for development)
6. Create OAuth 2.0 Client ID:
   - Application type: Web application
   - Authorized redirect URIs: 
     - `http://localhost:3001/api/auth/google/callback`
     - `http://localhost:3000/auth/callback`
7. Copy the Client ID and Client Secret

### 3. Email Configuration (SMTP)

#### Option A: Gmail SMTP (Recommended for Development)
1. Enable 2-Factor Authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. Use these settings:
   ```
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_SECURE=false
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-16-character-app-password
   ```

#### Option B: SendGrid (Production Recommended)
1. Sign up at [SendGrid](https://sendgrid.com/)
2. Create an API key
3. Use: `SENDGRID_API_KEY=your-sendgrid-api-key`

### 4. Payment Setup (Optional - for future implementation)

#### Stripe Setup
1. Sign up at [Stripe](https://stripe.com/)
2. Go to Developers → API keys
3. Copy your test keys:
   ```
   STRIPE_SECRET_KEY=sk_test_...
   STRIPE_PUBLISHABLE_KEY=pk_test_...
   ```
4. Set up webhooks:
   - Endpoint URL: `http://localhost:3001/api/webhooks/stripe`
   - Events: `payment_intent.succeeded`, `payment_intent.payment_failed`

### 5. File Storage Setup (Optional - for production)

#### AWS S3 Setup
1. Create AWS account and S3 bucket
2. Create IAM user with S3 permissions
3. Get access keys:
   ```
   AWS_ACCESS_KEY_ID=your-access-key
   AWS_SECRET_ACCESS_KEY=your-secret-key
   AWS_S3_BUCKET=bedazzled-uploads
   ```

#### Cloudinary Setup (Alternative)
1. Sign up at [Cloudinary](https://cloudinary.com/)
2. Get your credentials from dashboard:
   ```
   CLOUDINARY_CLOUD_NAME=your-cloud-name
   CLOUDINARY_API_KEY=your-api-key
   CLOUDINARY_API_SECRET=your-api-secret
   ```

## 🔐 Security Best Practices

### JWT Secret Generation
Generate a secure JWT secret:
```bash
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

### Environment Variables Security
- Never commit `.env` files to version control
- Use different secrets for development and production
- Rotate secrets regularly in production
- Use environment variable management tools in production (AWS Secrets Manager, etc.)

## 🚀 Quick Start Commands

### 1. Clone and Install Dependencies
```bash
# Frontend
pnpm install

# Backend
cd backend
pnpm install
```

### 2. Set Up Environment Variables
```bash
# Copy and configure frontend environment
cp .env.local.example .env.local
# Edit .env.local with your values

# Copy and configure backend environment
cd backend
cp .env.example .env
# Edit .env with your values
```

### 3. Start Development Servers
```bash
# Terminal 1: Start backend
cd backend
pnpm start:dev

# Terminal 2: Start frontend
pnpm dev
```

### 4. Access the Application
- Frontend: http://localhost:3001
- Backend API: http://localhost:3002/api
- API Documentation: http://localhost:3002/api/docs (if enabled)

## 🧪 Testing the Setup

### Test Database Connection
```bash
cd backend
pnpm start:dev
# Look for "Connected to MongoDB" in the logs
```

### Test Google OAuth
1. Go to http://localhost:3000
2. Click "Sign In"
3. Try Google OAuth login
4. Check if user is created in database

### Test File Upload
1. Go to admin dashboard
2. Try uploading a product image
3. Check if file is saved in `backend/uploads/products/`

### Test Email (if configured)
1. Create a custom order
2. Check if email notification is sent
3. Check email logs in console

## 🔧 Troubleshooting

### Common Issues

#### MongoDB Connection Failed
- Check if MongoDB is running: `brew services list | grep mongodb`
- Verify connection string format
- Check network connectivity for Atlas

#### Google OAuth Not Working
- Verify redirect URIs match exactly
- Check if APIs are enabled in Google Cloud Console
- Ensure OAuth consent screen is configured

#### Email Not Sending
- Verify SMTP credentials
- Check if 2FA and app password are set up correctly
- Test with a simple email client first

#### File Upload Issues
- Check if upload directory exists and has write permissions
- Verify file size limits
- Check allowed file types configuration

### Getting Help
- Check the console logs for detailed error messages
- Verify all environment variables are set correctly
- Ensure all required services are running
- Check network connectivity and firewall settings

## 📚 Additional Resources

- [MongoDB Atlas Documentation](https://docs.atlas.mongodb.com/)
- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Stripe API Documentation](https://stripe.com/docs/api)
- [SendGrid API Documentation](https://docs.sendgrid.com/)
- [Next.js Environment Variables](https://nextjs.org/docs/basic-features/environment-variables)
- [NestJS Configuration](https://docs.nestjs.com/techniques/configuration)

---

**🎉 Once everything is set up, you'll have a fully functional e-commerce platform ready for development and testing!**
