# Dependencies
node_modules
backend/node_modules
backend/dist

# Environment files
.env
.env.local
.env.*.local
backend/.env
backend/.env.*
COMPLETE_CREDENTIALS.env

# Development files
.next
.cache
.vscode
.idea

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache

# Next.js build output
.next/
out/

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Backend specific
backend/uploads
backend/logs

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation that's not needed in production
*.md
!README.md

# Test files
**/*.test.*
**/*.spec.*
__tests__/
test/
tests/

# Scripts that are not needed in production
scripts/
